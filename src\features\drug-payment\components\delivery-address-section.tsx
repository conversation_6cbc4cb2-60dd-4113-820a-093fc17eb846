import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { DeliveryAddress } from "../types";
import { useDrugPaymentStore } from "../hooks/use-drug-payment-store";
import { DeliveryAddressModal } from "./delivery-address-modal";

interface DeliveryAddressSectionProps {
  show: boolean;
}

export function DeliveryAddressSection({ show }: DeliveryAddressSectionProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { deliveryAddress, setDeliveryAddress } = useDrugPaymentStore();

  if (!show) return null;

  const formatAddress = (address: DeliveryAddress) => {
    const parts = [];
    if (address.street) parts.push(address.street);
    if (address.city) parts.push(address.city);
    if (address.lga) parts.push(address.lga);
    if (address.state) parts.push(address.state + " state");
    if (address.landmark) parts.push(`(Near ${address.landmark})`);
    return parts.join(", ");
  };

  return (
    <div className="mt-4">
      <h3 className="font-medium text-gray-900 mb-2">Delivery address</h3>
      <p className="text-sm text-gray-500 mb-4">
        Please, provide an address so we can get your drugs to you seamlessly.
      </p>

      {deliveryAddress ? (
        <div className="bg-gray-50 p-3 rounded-md">
          {/* Desktop layout: horizontal */}
          <div className="hidden sm:flex justify-between items-center">
            <p className="text-gray-700">{formatAddress(deliveryAddress)}</p>
            <Button
              variant="ghost"
              className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 px-3 py-1 h-auto text-sm"
              onClick={() => setIsModalOpen(true)}
            >
              Change address
            </Button>
          </div>

          {/* Mobile layout: stacked vertically */}
          <div className="sm:hidden">
            <p className="text-gray-700 mb-3">{formatAddress(deliveryAddress)}</p>
            <Button
              variant="ghost"
              className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 px-3 py-1 h-auto text-sm w-full"
              onClick={() => setIsModalOpen(true)}
            >
              Change address
            </Button>
          </div>
        </div>
      ) : (
        <Button
          variant="outline"
          className="w-full border-dashed"
          onClick={() => setIsModalOpen(true)}
        >
          Add delivery address
        </Button>
      )}

      <DeliveryAddressModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={(address) => {
          setDeliveryAddress(address);
          setIsModalOpen(false);
        }}
      />
    </div>
  );
}
