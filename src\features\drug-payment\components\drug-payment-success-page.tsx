import { But<PERSON> } from "@/components/ui/button";
import HealaLogoIcon from "@/components/icons/heala-logo-icon";
import { <PERSON><PERSON><PERSON>oader } from "@/components/heala-loader";
import { Info } from "lucide-react";
import { useQuery as useApolloQuery, useLazyQuery as useLazyApolloQuery } from "@apollo/client";
import { checkChargeQuery, getDrugOrderQuery, getRejectedDrugsQuery } from "@/graphql/queries";
import { useToast } from "@/hooks/use-toast";
import { useEffect } from "react";
import { HealaLogoWithText } from "@/components/icons/heala-logo-with-text";

interface DrugPaymentSuccessPageProps {
  orderId: string;
  reference?: string;
  referralId?: string;
}



export function DrugPaymentSuccessPage({ orderId, reference, referralId }: DrugPaymentSuccessPageProps) {
  const { toast } = useToast();

  // Authentication recovery query - only run if referralId is provided and no token exists
  const shouldRecoverAuth = referralId && !sessionStorage.getItem('token');
  const { loading: authRecoveryLoading, error: authRecoveryError } = useApolloQuery(getRejectedDrugsQuery, {
    variables: { referralId: referralId || "" },
    skip: !shouldRecoverAuth,
    fetchPolicy: 'network-only',
    onCompleted: (data) => {
      console.log("Authentication recovery completed");

      // Extract and store access token
      const accessToken = data.getRejectedDrugs.account?.access_token;
      if (accessToken) {
        sessionStorage.setItem('token', accessToken);
        console.log("Access token recovered and stored");
      } else {
        console.warn("No access token found in authentication recovery response");
        toast({
          title: "Authentication Recovery Failed",
          description: "Unable to recover authentication. Some features may not work properly.",
          variant: "destructive",
        });
      }

      // Fetch order data after auth recovery
      fetchOrderData();
    },
    onError: (error) => {
      console.error("Authentication recovery error:", error);
      toast({
        title: "Authentication Recovery Failed",
        description: "Unable to recover authentication. Please contact support if issues persist.",
        variant: "destructive",
      });
    }
  });

  // Payment verification query
  const { loading: paymentLoading, error: paymentError, data: paymentData } = useApolloQuery(checkChargeQuery, {
    variables: { data: { reference: reference || "" } },
    onError: (error) => {
      console.error("Payment verification error:", error);
      toast({
        title: "Payment Verification Failed",
        description: "Unable to verify your payment. Please contact support if you believe this is an error.",
        variant: "destructive",
      });
    }
  });

  // Order details query
  const [fetchOrderData, {
    data: orderData,
    loading: orderLoading,
    error: orderError
  }] = useLazyApolloQuery(getDrugOrderQuery, {
    variables: { id: orderId },
    onError: (error) => {
      console.error("Order details error:", error);
      toast({
        title: "Order Details Unavailable",
        description: "Unable to load order details. Please contact support for assistance.",
        variant: "destructive",
      });
    }
  });

  // Fetch order data immediately if authentication recovery is not needed 
  // else wait for authentication recovery to complete (data is fetched in auth recovery onComplete)
  useEffect(() => {
    if (!shouldRecoverAuth) {
      fetchOrderData();
    }
  }, [shouldRecoverAuth, fetchOrderData]);

  // Show error if reference is missing
  useEffect(() => {
    if (!reference) {
      toast({
        title: "Invalid Payment Reference",
        description: "Payment reference is missing. Please contact support for assistance.",
        variant: "destructive",
      });
    }
  }, [reference, toast]);

  const isLoading = paymentLoading || orderLoading || authRecoveryLoading;
  const hasErrors = paymentError || orderError || authRecoveryError || !reference;

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <HealaLoader />
      </div>
    );
  }

  if (hasErrors) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen bg-gray-50 p-4">
        <div className="mb-6">
          <HealaLogoIcon className="w-16 h-16" />
        </div>
        <div className="bg-white rounded-lg shadow-md w-full max-w-2xl p-8 text-center">
          <div className="text-red-500 text-xl mb-4">Unable to load order details</div>
          <p className="text-gray-600 mb-6">
            There was an issue loading your order information. Please contact support for assistance.
          </p>
          <Button asChild>
            <a href="mailto:<EMAIL>?subject=Order Support Request">
              Contact Support
            </a>
          </Button>
        </div>
      </div>
    );
  }

  const order = orderData?.getDrugOrder;

  if (!order) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen bg-gray-50 p-4">
        <div className="mb-6">
          <HealaLogoIcon className="w-16 h-16" />
        </div>
        <div className="bg-white rounded-lg shadow-md w-full max-w-2xl p-8 text-center">
          <div className="text-gray-500 text-xl mb-4">Order not found</div>
          <p className="text-gray-600 mb-6">
            We couldn't find the order details. Please contact support for assistance.
          </p>
          <Button asChild>
            <a href="mailto:<EMAIL>?subject=Order Not Found">
              Contact Support
            </a>
          </Button>
        </div>
      </div>
    );
  }

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Format time for display
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 2
    }).format(amount);
  };

  return (
    <div className="flex flex-col justify-center items-center min-h-screen bg-gray-50 p-4">
      {/* Heala Logo */}
      <div className="mb-10 mt-4 lg:mt-6">
        <HealaLogoWithText />
      </div>

      <div className="bg-white rounded-lg shadow-md w-full max-w-2xl">
        {/* Success Header */}
        <div className="text-center p-8 pb-6">
          <div className="flex justify-center mb-6">
            <img src="/check-green.svg" alt="Success" className="w-16 h-16" />
          </div>

          <h1 className="text-2xl font-bold text-emerald-600 mb-4">
            Your order is confirmed!
          </h1>

          <p className="text-gray-600">
            Thank you for your purchase. Your order details are below.
          </p>
        </div>

        {/* Order Details Card */}
        <div className="mx-6 mb-6 border border-gray-200 rounded-lg">
          {/* Order Header */}
          <div className="flex justify-between items-center p-4 border-b border-gray-200">
            <div>
              <span className="text-sm text-gray-500">Order ID: </span>
              <span className="font-medium">{order.orderId}</span>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500">{formatDate(order.createdAt)}</div>
              <div className="text-sm text-gray-500">{formatTime(order.createdAt)}</div>
            </div>
          </div>

          {/* Purchase Information */}
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-sm font-medium text-blue-600 mb-3">Purchase Information</h3>
            <div className="space-y-6">
              {order.prescriptions?.map((prescription, index) => (
                <div key={index} className="flex justify-between items-start gap-2">
                  <div className="flex-1 flex flex-col gap-1">
                    <div className="font-medium text-gray-900">
                      {prescription.drugName} {prescription.drugForm && `(${prescription.drugForm})`}
                    </div>
                    <div className="text-sm text-gray-500">
                      Dosage: {prescription.dosageUnit}
                    </div>
                    <div className="text-sm text-gray-500">
                      Quantity: {prescription.quantity}
                    </div>
                  </div>
                  <div className="font-medium text-primary">
                    {formatCurrency(prescription.markedUpDrugPrice ? prescription.markedUpDrugPrice : 0)}
                  </div>
                </div>
              )) || (
                  <div className="text-gray-500">No prescription information available</div>
                )}
            </div>
          </div>

          {/* Delivery/Pickup Information */}
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-sm font-medium text-blue-600 mb-3">
              {order.deliveryOption === 'pickup' ? 'Pickup Information' : 'Delivery Information'}
            </h3>

            {order.deliveryOption === 'pickup' ? (
              <div className="bg-primary-50 p-4 rounded-lg flex flex-col gap-4">
                <div className="">
                  <span className="font-medium">Pharmacy Name: </span>
                  {order.pharmacyName}
                </div>
                <div className="">
                  <span className="font-medium">Pharmacy Address: </span>
                  {order.pharmacyAddress}
                </div>
              </div>
            ) : (
              order.userLocation && (
                <div className="text-gray-700">
                  <div>{order.userLocation.address}</div>
                  <div className="text-sm text-gray-600 mt-1">
                    {order.userLocation.city}, {order.userLocation.state}
                  </div>
                  {order.userLocation.landmark && (
                    <div className="text-sm text-gray-600">
                      Landmark: {order.userLocation.landmark}
                    </div>
                  )}
                </div>
              )
            )}
          </div>

          {/* Payment Information */}
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-sm font-medium text-blue-600 mb-3">Payment Information</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Payment Method</span>
                <span className="font-medium">{paymentData?.checkCharge?.chargeResponse?.channel === 'card' ? 'Debit Card' : 'Paystack'}</span>
              </div>
              {paymentData?.checkCharge?.chargeResponse?.channel === 'card' && <div className="flex justify-between">
                <span className="text-gray-600">Card Type</span>
                <span className="font-medium">Visa</span>
              </div>}
              {paymentData?.checkCharge?.chargeResponse?.channel === 'card' && <div className="flex justify-between">
                <span className="text-gray-600">Card Number</span>
                <span className="font-medium">**** **** **** {paymentData?.checkCharge?.chargeResponse?.authorization?.last4}</span>
              </div>}
            </div>
          </div>

          {/* Order Summary */}
          <div className="p-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Subtotal</span>
                <span className="font-medium">
                  {formatCurrency(order.total || 0)}
                </span>
              </div>
              {order.deliveryOption === 'home' && (order.deliveryFee || 0) > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Shipping</span>
                  <span className="font-medium">{formatCurrency(order.deliveryFee || 0)}</span>
                </div>
              )}
              <div className="border-t border-gray-200 pt-2">
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total</span>
                  <span>{formatCurrency(order.deliveryOption === 'pickup' ? (order.total || 0) : (order.total || 0) + (order.deliveryFee || 0))}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Track Order Button */}
        {order.trackingLink && (
          <div className="px-6 pb-4">
            <Button
              className="w-full bg-blue-600 hover:bg-blue-700"
              asChild
            >
              <a
                href={order.trackingLink}
                target="_blank"
                rel="noopener noreferrer"
              >
                Track Your Order
              </a>
            </Button>
          </div>
        )}

        {/* Support Information */}
        <div className="p-6 bg-blue-50 rounded-b-lg">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">
                An email containing this information has been sent to you and if you have any questions about your order, please contact our customer support team{' '}
                <a
                  href="mailto:<EMAIL>"
                  className="underline hover:no-underline"
                >
                  <EMAIL>
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}