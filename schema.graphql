schema {
  query: Query
  mutation: Mutation
}

input AcceptConsultationInput {
  doctor: ID!
  id: ID!
}

type Account {
  _id: ID
  access_token: String
  apiKey: String
  authType: String
  createdAt: DateTime
  deactivateType: String
  deactivated: Boolean
  deactivatedAt: String
  dociId: String
  email: String
  enrolleeNumber: String
  isActive: Boolean
  isEmailVerified: Boolean
  isPasswordTemporary: Boolean
  lastLogin: DateTime
  nextStep: String
  otp: String
  otpExpiresAt: DateTime
  providerId: String
  refresh_token: String
  role: Role
  type: String
  updatedAt: DateTime
  userTypeId: UserType
}

type AccountConnection {
  data: [PopulatedAccount!]!
  pageInfo: PageInfo
}

type AccountData {
  _id: ID
  apiKey: String
  authType: String
  createdAt: DateTime
  deactivateType: String
  deactivated: Boolean
  deactivatedAt: String
  dociId: String
  email: String
  enrolleeNumber: String
  isActive: Boolean
  isEmailVerified: Boolean
  isPasswordTemporary: Boolean
  nextStep: String
  otp: String
  otpExpiresAt: DateTime
  providerId: ID
  role: Role
  updatedAt: DateTime
  userTypeId: ID
}

type AccountDetail {
  accountName: String
  accountNumber: String
  bankName: String
  nuban: String
  recipientCode: String
}

type AccountDetails {
  accountName: String
  accountNumber: String
  bankName: String
  nuban: String
}

type AccountPayload {
  account: Account
  errors: ErrorPayload
  message: String
}

type AccountProfile {
  account: AccountData
  profile: CreateProfile
}

type ActivityLog {
  _id: ID
  arguments: JSON
  email: String
  endpoint: String
  ipAddress: String
  operation: String
  result: JSON
  timestamp: DateTime
  userAgent: String
  userId: ID
}

input AddBankAccountInput {
  accountName: String
  accountNumber: String
  bankName: String
  doctorId: String
  nuban: String
}

input AddDependantInput {
  dob: String!
  email: String!
  firstName: String
  gender: String!
  image: String
  lastName: String
  phoneNumber: String!
  relationship: String!
}

input AddDependants {
  dependants: [AddDependantInput!]
  employeeId: String!
}

input AddDiagnosticReferralInput {
  consultationId: String
  doctor: String!
  labInfo: LabInfoInput
  note: String
  patient: String!
  provisionalDiagnosis: String
  reason: String!
  referralId: String!
  sampleCollection: String!
  total: Float
  userLocation: UserLocationInput
}

input AddDiagnosticTestInput {
  id: String!
  time: String!
}

input AddDoctorConsultationInput {
  consultationId: String!
  contactMedium: String
  diagnosis: [Diagnosis!]
  doctor: String!
  doctorNote: String
  joined: Boolean
  referralId: String
  status: String
  type: String
}

input AddDrugOrderInput {
  consultationId: String!
  deliveryOption: String!
  doctor: String!
  note: String
  patient: String!
  pharmacyAddress: String
  pharmacyCode: String
  pharmacyName: String
  prescriptionDate: String
  prescriptions: [JSON!]
  userLocation: UserLocationInput
}

input AddEmployeeInput {
  businessId: String!
  dependants: [AddDependantInput!]
  dob: String!
  email: String!
  firstName: String!
  gender: String!
  image: String
  lastName: String!
  noofdependants: Float
  phoneNumber: String!
  type: String!
}

input AddHospitalMedicationInput {
  consultationId: String
  deliveryOption: String
  doctor: String!
  drugs: [DrugsInput!]!
  note: String
  partner: String!
  patient: String!
  pharmacyCode: String
  prescriptionDate: String
  userLocation: UserLocationInput
}

input AddPartnerInput {
  address: String
  bankDetails: [BankDetailsInput!]
  category: String!
  classification: String
  email: String!
  logoImageUrl: String!
  name: String!
  phone: String
  providerId: String!
  specialisation: String
}

input AddPastIllnessInput {
  id: ID!
}

input AddPrescription {
  consultationId: String!
}

input AddPrescriptionReferralInput {
  consultationId: String!
  drugs: [DrugInput!]
  drugsNote: String
  specialist: [SpecialistInput!]
  tests: [TestInput!]
  testsNote: String
}

input AddPrescriptionsInput {
  consultationId: String!
  prescriptions: [PrescriptionInput!]!
}

input AddQuoteInput {
  comment: String
  companyName: String!
  email: String!
  firstName: String!
  lastName: String!
  noOfEmployees: Float!
  phoneNumber: String!
  state: String!
}

input AddRatingInput {
  comment: String
  consultationId: String!
  review: String
  score: Float!
}

input AddSpecializationInput {
  name: String
}

type AllProviderConnection {
  pageInfo: PageInfo
  provider: [ProvidersType!]
}

type AllStats {
  availabilityCalender: AvailabilityCalender
  consultationStats: Stat
  doctorStats: Stat
  drugEarningStats: Stat
  earningStats: Stat
  enrolleeStats: Stat
  partnerStats: Stat
  patientStats: Stat
  payoutStats: Stat
  subscriptionEarningStats: Stat
  subscriptionStats: Stat
  testEarningStats: Stat
}

type Allergy {
  _id: ID
  createdAt: DateTime
  food: String
  medication: String
  profile: Profile
  severity: String
  updatedAt: DateTime
}

type AllergyArrayPayload {
  allergies: [Allergy!]!
  pageInfo: PageInfo!
}

input AllergyInput {
  id: String!
}

type AllergyPayload {
  allergy: Allergy!
}

type AllowedFeatures {
  consultation: String
}

input AlumniAssociation {
  facebook_group_name: String!
  instagram_handle: String!
}

type ApikeyPayload {
  account: AccountData!
  message: String!
}

type Appointment {
  _id: ID!
  createdAt: DateTime
  date: String
  description: String
  doctor: DoctorProfile
  patient: Profile
  providerId: Provider
  time: String
  updatedAt: DateTime
}

type AppointmentConnection {
  data: [Appointment!]!
  pageInfo: PageInfo
}

input AppointmentInput {
  id: String!
}

type AppointmentPayload {
  appointment: Appointment
  errors: [ErrorPayload!]
}

type AppointmentStats {
  totalPast: Float
  totalUpcoming: Float
}

type Availability {
  _id: ID
  available: Boolean
  createdAt: DateTime
  day: String
  doctor: DoctorProfile
  providerId: String
  times: [AvailabilityTypeArray!]
  updatedAt: DateTime
}

input AvailabilityArray {
  start: String!
  stop: String!
}

type AvailabilityCalender {
  availableDoctors: [DoctorAvailabilityStat!]
  today: String
}

type AvailabilityConnection {
  availability: [Availability!]
  bookedTimes: [BookedConsultationTime!]
  errors: [ErrorPayload!]
  pageInfo: PageInfo
}

type AvailabilityPayload {
  availability: Availability
  errors: [ErrorPayload!]
}

type AvailabilityTypeArray {
  _id: String
  available: Boolean
  passed: Boolean
  start: String
  stop: String
}

input AvailableDoctorTimesInput {
  date: String
  day: String
  doctor: String!
}

input AvailableIdInput {
  id: String!
}

type AvailableTime {
  _id: ID!
  available: Boolean!
  createdAt: DateTime!
  day: String!
  providerId: String!
  times: [AvailabilityTypeArray!]!
  updatedAt: DateTime!
}

type AvailableTimeConnection {
  data: [AvailableTime!]!
  pageInfo: PageInfo
}

input AvailableTimeIdInput {
  id: String!
}

input AvailableTimeInput {
  day: String!
  providerId: String!
  times: [AvailabilityArray!]!
}

type AvailableTimePayload {
  availableTimes: AvailableTime
  errors: [ErrorPayload!]
}

type AvailableTimeStats {
  _id: ID
  available: Boolean
  createdAt: DateTime
  day: String!
  doctor: DoctorProfile
  providerId: String
  times: [AvailabilityTypeArray!]
  updatedAt: DateTime
}

type BankDetails {
  accName: String
  accNumber: String
  default: Boolean
  name: String
  nuban: String
}

input BankDetailsInput {
  accName: String
  accNumber: String
  default: Boolean
  name: String
  nuban: String
}

type Benefit {
  code: Float
  dateAdded: DateTime
  description: String
  level: Float
  limit: Float
  name: String
}

input BenefitInput {
  code: Float
  dateAdded: DateTime
  description: String!
  level: Float
  limit: Float
  name: String!
}

type BenefitPayload {
  code: Float
  dateAdded: DateTime
  description: String
  level: Float
  limit: Float
  name: String
}

type BookedConsultationTime {
  date: String
  day: String
  hour: Int
  minute: Int
  year: Int
}

type BookedConsultationTimeConnection {
  data: [BookedConsultationTime!]!
}

input BookedConsultationTimeInput {
  date: String
  day: String
  doctor: String!
}

input BulkUpdateInput {
  ids: [String!]
  referralId: String!
  status: String!
}

type Business {
  address: String
  createdAt: DateTime
  email: String
  name: String
  noOfEmployees: Float
  providerId: String
  type: String
  updatedAt: DateTime
  userTypeId: String
}

input CancelConsultationInput {
  id: String!
  reason: String
}

input CancelDiagnosticTestInput {
  id: String!
  reason: String!
}

input CancelDisputeInput {
  disputeResolvedReason: String!
  id: String!
}

input CancelDrugOrderInput {
  id: String!
  reason: String!
}

type CancelSubscriptionConnection {
  cancelSubscription: Boolean!
}

type Card {
  _id: String!
  authorization: String!
  bin: String!
  brand: String!
  createdAt: DateTimeScalar!
  expMonth: String!
  expYear: String!
  last4: String!
  reusable: String!
  signature: String!
  updatedAt: DateTimeScalar!
}

input CardInput {
  user: String!
}

type CardsConnections {
  data: [Card!]!
}

input ChangeApprovalStatus {
  id: String!
  status: String!
}

type ChatConversation {
  _id: String
  lastMessage: ChatMessage
  partner: JSON
  user: JSON
}

type ChatConversationsConnection {
  data: [ChatConversation!]!
}

type ChatMessage {
  _id: String
  consultationId: String
  content: String
  createdAt: DateTime
  read: Boolean
  received: Boolean
  receiver: JSON
  receiverData: JSON
  receiverRole: String
  sender: JSON
  senderData: JSON
  senderRole: String
  sent: Boolean
  updatedAt: DateTime
}

input ChatMessageInput {
  consultationId: String!
  content: String!
  receiver: String!
  receiverRole: String!
  sender: String!
  senderRole: String!
}

type ChatMessagePayload {
  errors: [ErrorPayload!]
  message: ChatMessage
}

type ChatMessagesPayload {
  data: [ChatMessage!]
  errors: [ErrorPayload!]
}

input ChatMessagesUploadInput {
  consultationId: String!
  messages: [UploadChatMessageInput!]
}

type CheckChargeConnection {
  chargeResponse: CheckChargeResponse!
}

input CheckChargeInput {
  reference: String!
}

type CheckChargeResponse {
  amount: String
  authorization: PaystackAuthorization
  channel: String
  currency: String
  gateway_response: String
  id: String
  message: String
  paid_at: String
  reference: String
  status: String
}

type CheckHasSubscriptionConnection {
  hasSubscription: Boolean!
}

type Company {
  _id: ID
  address: String
  createdAt: DateTime
  email: String
  enrolleeCount: Int
  logo: String
  name: String
  phone: String
  providerId: String!
  updatedAt: DateTime
}

type CompanyConnection {
  data: [Company!]
  pageInfo: PageInfo
}

input CompanyInput {
  id: String!
}

type CompanyPayload {
  company: Company
  message: String
}

input CompleteDiagnosticTestInput {
  id: String!
  testResults: [TestResultsInput!]!
}

input CompletePasswordInput {
  email: String!
  otp: String!
  password: String!
}

input ConfirmEmployeeInput {
  accept: Boolean!
  profileId: String
  token: String!
}

input ConfirmInviteInput {
  accept: Boolean!
  profileId: String!
  token: String!
}

input ConfirmPatientInviteInput {
  accept: Boolean!
  patient: String!
  token: String!
}

type ConsultType {
  consultation: String
}

type Consultation {
  _id: ID!
  appointmentAcceptedAt: String
  appointmentStartedAt: String
  companyId: Company
  consultationDuration: String
  consultationOwner: String
  contactMedium: String
  createdAt: DateTime
  createdThrough: String
  declineReason: String
  description: String
  diagnosis: [Diag!]
  discomfortLevel: String
  disputeReason: String
  disputeResolvedReason: String
  disputeStatus: String
  doctor: Doctor
  doctorEndCommunicationReason: String
  doctorJoined: Boolean
  doctorNote: String
  doctorSatisfactionReason: String
  doctorSatisfied: Boolean
  externalPharmacy: String
  externalProvider: String
  fee: Int
  firstNotice: String
  followUpConsultationId: JSON
  isDisputeResolved: Boolean
  isDisputed: Boolean
  isFollowUp: Boolean
  isNemEnrollee: Boolean
  joined: Boolean
  notificationSchedules: [NotificationSchedule!]
  paid: Boolean
  path: String
  patient: Profile
  patientEndCommunicationReason: String
  patientJoined: Boolean
  patientSatisfactionReason: String
  patientSatisfied: Boolean
  pharmacyAddress: String
  pharmacyCode: String
  pharmacyName: String
  prescription: [Prescription!]
  principalHmoId: String
  providerId: String
  rating: Rating
  reason: String
  referralId: String
  status: String
  symptoms: [Symp!]
  time: DateTime
  trackingId: String
  type: String
  updatedAt: DateTime
  wasDisputed: Boolean
}

input ConsultationChatMessageInput {
  content: String!
  receiver: String!
  receiverRole: String!
  sender: String!
  senderRole: String!
}

type ConsultationCombinedPayload {
  consultation: Consultation
  diagnosis: DiagnosticLabTest
  errors: [ErrorPayload!]
  prescription: SinglePrescription
  referral: SingleReferral
}

type ConsultationConnection {
  data: [Consultation!]
  pageInfo: PageInfo
}

input ConsultationId {
  id: ID!
}

type ConsultationPayload {
  consultation: Consultation
  errors: [ErrorPayload!]
}

type Counts {
  activeCount: [JSON!]
  inactiveCount: [JSON!]
}

input CreateAllergyInput {
  food: String
  medication: String
  profile: String!
  severity: String!
}

input CreateAppointmentInput {
  date: String!
  description: String!
  doctor: String!
  patient: String!
  providerId: String!
  time: String!
}

input CreateBusinessInput {
  address: String!
  name: String!
  noOfEmployees: Float!
  type: String!
}

type CreateBusinessPayload {
  business: Business
}

input CreateCompanyInput {
  address: String
  email: String
  externalId: String
  logo: String
  name: String!
  phone: String
  providerId: String!
}

input CreateConsultationInput {
  consultationOwner: String!
  contactMedium: String
  createdThrough: String
  description: String
  discomfortLevel: String!
  doctor: String
  externalPharmacy: String
  externalProvider: String
  fee: Float
  firstNotice: String!
  followUpConsultationId: String
  isFollowUp: Boolean
  joined: Boolean
  paid: Boolean
  path: String
  patient: String!
  pharmacyAddress: String
  pharmacyCode: String
  pharmacyName: String
  providerId: String
  referral: Boolean
  referralId: String
  status: String
  symptoms: [Symptom!]!
  time: String
  type: String
}

type CreateCurrentIllness {
  _id: ID!
  abilityToFunction: String!
  createdAt: DateTime!
  doctor: ID!
  howLongDoesItLast: String!
  intensity: String!
  locationOfProblem: String!
  otherPain: String!
  patient: ID!
  problemWorse: String!
  scaleOfDiscomfort: String!
  timeOfFirstNotice: String!
  updatedAt: DateTime!
}

input CreateCurrentIllnessInput {
  abilityToFunction: String!
  doctor: ID!
  howLongDoesItLast: String!
  intensity: String!
  locationOfProblem: String!
  otherPain: String!
  patient: ID!
  problemWorse: String!
  scaleOfDiscomfort: String!
  timeOfFirstNotice: String!
}

input CreateDisputeInput {
  disputeReason: String!
  id: ID!
}

input CreateDoctorPatientInput {
  doctor: String!
  email: String!
  name: String!
  patient: String
}

input CreateDoctorProfileAccountInput {
  cadre: String!
  dob: DateTime
  email: String!
  fee: Float
  firstName: String!
  gender: String
  hospital: String
  idCard: String
  image: String
  lastName: String!
  phoneNumber: String!
  providerId: String!
  specialization: String!
  timezoneOffset: String
}

input CreateDoctorProfileInput {
  cadre: String!
  dob: DateTime
  dociId: String!
  fee: Float
  firstName: String!
  gender: String
  hospital: String
  idCard: String
  image: String
  lastName: String!
  phoneNumber: String!
  providerId: String!
  specialization: String
  timezoneOffset: String
}

input CreateEarningInput {
  balance: Float!
  consultationId: String
  doctor: String!
  providerId: String
}

input CreateEnrolleeInput {
  client: String
  companyId: String
  email: String
  expiryDate: DateTime!
  firstName: String!
  hmoId: String!
  isMicroInsurance: Boolean
  lastName: String!
  noc: Int
  phone: String
  photo: String
  plan: String
  planId: String
  providerId: String!
}

input CreateEnterpriseInput {
  address: String!
  businessType: String!
  dateFounded: String
  email: String!
  firstName: String!
  image: String!
  industry: String!
  lastName: String!
  name: String!
  noOfEmployees: Float!
  phoneNumber: String!
  planId: String!
  startDate: String
}

input CreateFamilyInput {
  admin: String!
  dob: String!
  email: String
  firstName: String!
  gender: String!
  image: String
  lastName: String!
  phoneNumber: String
  relationship: String!
}

input CreateIllnessInput {
  description: String!
  name: String!
}

input CreateInsurancePlanInput {
  amount: Float!
  benefits: [BenefitInput!]
  code: String!
  description: String!
  duration: String!
  externalProviderName: String!
  image: String
  name: String!
  type: String!
}

input CreateLabResultInput {
  doctor: String!
  partner: String!
  patient: String!
  url: String!
}

input CreateMessageInput {
  body: String!
  recipient: String!
  sender: String!
  subject: String!
}

input CreatePatientMedicationInput {
  doctor: String!
  dosage: Float!
  interval: String!
  name: String!
  patient: String!
}

input CreatePatientProfileInput {
  bloodGroup: String
  dob: String
  dociId: String
  email: String
  firstName: String!
  gender: String!
  genotype: String
  height: Int
  hmoId: String
  image: String
  lastName: String!
  phoneNumber: String!
  providerId: ID!
  roleId: ID!
  timezoneOffset: String
  weight: Int
}

input CreatePayoutInput {
  amount: Float!
  doctor: String!
  providerId: String
  status: String!
}

input CreatePayoutsInput {
  providerId: String!
}

input CreatePermissionInput {
  description: String
  name: String!
}

input CreatePlanInput {
  allowedFeatures: JSON
  amount: Float!
  description: String
  duration: String
  image: String
  name: String!
  noOfDoctors: Float
  provider: ID
  specialisation: String
  type: String
}

type CreateProfile {
  _id: ID!
  accountId: ID
  address: String
  bloodGroup: String
  consultations: Int
  createdAt: DateTimeScalar!
  dob: DateTime
  dociId: String!
  email: String
  externalPlanCode: String
  externalPlanType: String
  externalProvider: String
  externalProviderId: String
  firstName: String!
  gender: String
  genotype: String
  height: Float
  hmoId: String
  image: String
  lastName: String!
  pastIllness: [PastIllnessType!]
  phoneNumber: String
  providerId: String
  rating: Int
  status: String
  subscriptionId: ID
  timezoneOffset: String
  updatedAt: DateTimeScalar!
  weight: Float
}

input CreateProfileInput {
  bloodGroup: String
  dob: String
  firstName: String!
  gender: String!
  genotype: String
  height: Int
  hmoId: String
  image: String
  lastName: String!
  phoneNumber: String!
  timezoneOffset: String
  weight: Int
}

input CreateProviderInput {
  address: String
  consultationLimitType: String
  email: String
  hmoPlans: [HmoPlansInput!]
  icon: String
  iconAlt: String
  isWellaHealthIntegration: Boolean
  monthlyConsultationLimit: Int
  name: String!
  phone: String
  planId: String
  rareCase: Boolean
  userTypeId: String!
}

input CreateReminderInput {
  date: DateTime!
  description: String!
  interval: String
  patient: String!
  type: String!
}

input CreateRequestInput {
  firstName: String!
  lastName: String!
  location: String!
  name: String!
  type: String!
}

input CreateRoleInput {
  description: String
  editable: Boolean
  name: String!
  permissions: [String!]!
}

input CreateSettingInput {
  key: String
  name: String!
  value: String!
}

input CreateTicketInput {
  createdBy: String!
  description: String
  priority: String
  status: String
  title: String!
  type: String
}

input CreateUserTypeInput {
  description: String
  icon: String
  name: String!
}

input CreateVerificationInput {
  alumni_association: AlumniAssociation!
  external_reference: ExternalReference!
  license: License!
  profileId: String!
  qualification: Qualification!
  reference: Reference!
  status: Boolean
  yearbook: Yearbook!
}

type CurrentIllness {
  _id: ID
  abilityToFunction: String
  createdAt: DateTimeScalar
  doctor: String!
  howLongDoesItLast: String
  intensity: String
  locationOfProblem: String
  otherPain: String
  patient: String!
  problemWorse: String
  scaleOfDiscomfort: String
  timeOfFirstNotice: String
  updatedAt: DateTimeScalar
}

type CurrentIllnessPayload {
  currentIllness: CurrentIllness
  errors: [ErrorPayload!]
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

"""Date custom scalar type"""
scalar DateTimeScalar

type DaysStats {
  active: Float
  fiveDays: Counts
  inactive: Float
  oneDay: Counts
  oneMonth: Counts
  oneYear: Counts
  threeMonths: Counts
}

input DeactivateCompanyEnrolleeInput {
  companyId: String!
}

input DeactivateEnrolleeInput {
  id: String!
}

input DeclineConsultationInput {
  id: ID!
  reason: ID!
}

input DeleteAllergyInput {
  id: String!
}

type DeleteAvailableTimePayload {
  availability: Availability
  errors: [ErrorPayload!]
}

type DeleteConsultationPayload {
  count: Int
  errors: [ErrorPayload!]
  message: String
}

type DeleteDoctorPatientPayload {
  count: Int
  errors: [ErrorPayload!]
  message: String
}

input DeleteEnrolleeInput {
  id: String!
}

input DeleteFamilyInput {
  id: String!
}

input DeleteLabInput {
  id: String!
}

input DeleteMyAccountInput {
  deactivateType: String!
  deleteReason: String!
  password: String!
}

type DeletePermissionPayload {
  count: Float!
  errors: [ErrorPayload!]!
  message: String!
}

input DeletePlanInput {
  id: String!
}

input DeleteProfileInput {
  id: String!
}

input DeleteProviderInput {
  id: ID!
}

type DeleteProviderPayload {
  count: Float!
  message: String!
}

input DeleteRoleInput {
  id: String!
}

input DeleteSettingInput {
  id: String!
}

type DeleteSettingPayload {
  count: Float!
  errors: [ErrorPayload!]!
  message: String!
}

input DeleteUserTypeInput {
  id: String!
}

type Dependant {
  dob: String
  email: String
  firstName: String
  lastName: String
  phoneNumber: String
  relationship: String
}

type Diag {
  ailment: String!
  severity: String!
}

input Diagnosis {
  ailment: String!
  severity: String!
}

type DiagnosisConnection {
  diagnosis: [SymptomsDiagnosis!]!
}

type DiagnosticCalendarPayload {
  month: Float
  sum: Float
  year: Float
}

type DiagnosticDashboard {
  cancelledTestsCount: Float
  cancelledTestsStats: [DiagnosticCalendarPayload!]
  completedTestsCount: Float
  completedTestsStats: [DiagnosticCalendarPayload!]
  scheduledTestsCount: Float
  scheduledTestsStats: [DiagnosticCalendarPayload!]
  testRequestsCount: Float
  testRequestsStats: [DiagnosticCalendarPayload!]
}

input DiagnosticInput {
  id: String!
}

type DiagnosticLabTest {
  _id: ID
  createdAt: DateTime
  markedUpTestPrice: Float
  name: String
  note: String
  paid: Boolean
  partner: String
  price: Float
  tat: String
  testPriceMarkUp: Float
  updatedAt: DateTime
  urgency: String
}

input DiagnosticLabTestInput {
  name: String!
  note: String
  price: Float!
  tat: String!
  urgency: String
}

type DiagnosticLabTestPayload {
  diagnosticLabTest: DiagnosticLabTest
}

type DiagnosticLabTestsConnection {
  data: [DiagnosticLabTest!]
  pageInfo: PageInfo
}

type DiagnosticTest {
  _id: ID!
  cancellationReason: String
  consultationId: String
  createdAt: DateTime
  diagnosticReferral: String
  doctor: DoctorProfile
  labInfo: LabInfo
  note: String
  partner: Partner
  patient: Profile
  provisionalDiagnosis: String
  reason: String
  referralId: String
  sampleCollection: String
  scheduledAt: DateTime
  status: String
  testId: String
  testPickUpFee: Float
  testResults: [TestResults!]
  tests: [DiagnosticLabTest!]
  time: String
  total: Float
  trackingId: String
  updatedAt: DateTime
  userLocation: Location
}

type DiagnosticTestPayload {
  diagnosticTest: DiagnosticTest
}

type DiagnosticTestsConnection {
  data: [DiagnosticTest!]
  pageInfo: PageInfo
}

input DisableAccountInput {
  deactivateType: String!
}

type Doctor {
  _id: String
  accountDetails: AccountDetail
  balance: Float
  cadre: String
  dob: DateTime
  dociId: String!
  email: String
  firstName: String
  gender: String
  hospital: String
  lastName: String
  phoneNumber: String
  picture: String
  providerId: ID
  rating: Float
  specialization: String
  timezoneOffset: String
}

type DoctorAvailabilityStat {
  availability: Availability
  dociId: String
  firstName: String
  lastName: String
  providerId: String
}

type DoctorPatient {
  _id: ID!
  createdAt: DateTime
  doctor: DoctorProfile
  doctorData: JSON
  email: EmailAddress
  isActive: Boolean
  name: String
  patient: Profile
  patientData: JSON
  status: String
  token: String
  tokenExpiresAt: String
  updatedAt: DateTime
}

type DoctorPatientConnection {
  data: [DoctorPatient!]!
  errors: [ErrorPayload!]
  pageInfo: PageInfo
}

input DoctorPatientIdInput {
  id: String!
}

type DoctorPatientPayload {
  errors: [ErrorPayload!]
  patient: DoctorPatient
}

type DoctorProfile {
  _id: ID
  accountDetails: AccountDetails
  accountId: Account
  address: String
  balance: Int
  cadre: String
  consultationReminderNotification: Boolean
  consultationRequestNotification: Boolean
  createdAt: DateTime
  dob: DateTime
  dociId: String
  email: String
  fee: Float
  firstName: String
  gender: String
  hospital: String
  image: String
  instantConsultationNotification: Boolean
  lastName: String
  phoneNumber: String
  picture: String
  providerId: ProviderType
  rating: Int
  specialization: String
  status: String
  updatedAt: DateTime
}

type DoctorProfileArrayPayload {
  errors: [ErrorPayload!]
  pageInfo: PageInfo
  profile: [DoctorProfile!]
}

input DoctorProfileCountInput {
  filterBy: JSON
  q: String
}

input DoctorProfileInput {
  id: String!
}

type DoctorProfilePayload {
  profile: DoctorProfile
}

type DoctorThreshold {
  _id: String
  createdAt: DateTime
  end: Float
  priority: Float
  start: Float
  title: String
  updatedAt: DateTime
}

type DosageFreq {
  duration: Float
  timing: Float
}

input DosageFrequency {
  duration: Float
  timing: Float
}

input DosageFrequencyInput {
  duration: Float
  timing: Float
}

type Drug {
  _id: String
  amount: Float
  approved: String
  dosageFrequency: ReferralDosageFrequency
  dosageQuantity: Float
  dosageUnit: String
  drugForm: String
  drugName: String
  drugPrice: Float
  instructions: String
  markedUpPrice: Float
  markup: Float
  notes: String
  paid: Boolean
  priceListId: Float
  quantity: Float
  route: String
  unitPrice: Float
}

input DrugInput {
  amount: Float
  dosageFrequency: DosageFrequencyInput
  dosageQuantity: Float
  dosageUnit: String
  drugForm: String
  drugName: String!
  drugPrice: Float
  instructions: String
  markedUpPrice: Float
  markup: Float
  notes: String
  priceListId: Float
  quantity: Float
  route: String
  unitPrice: Float
}

type DrugOrder {
  _id: ID
  cancellationReason: String
  consultationId: String
  createdAt: DateTime
  deliveryFee: Float
  deliveryOption: String
  doctor: DoctorProfile
  note: String
  orderId: String
  partner: Partner
  patient: PatientProfile
  pharmacyAddress: String
  pharmacyCode: String
  pharmacyName: String
  prescriptionDate: DateTime
  prescriptions: [DrugPrescription!]
  status: String
  total: Float
  trackingId: String
  trackingLink: String
  updatedAt: DateTime
  userLocation: Location
}

input DrugOrderInput {
  id: String!
}

type DrugOrderPayload {
  drugOrder: DrugOrder!
  errors: [ErrorPayload!]!
}

type DrugOrdersConnection {
  data: [DrugOrder!]!
  pageInfo: PageInfo!
}

type DrugPrescription {
  dosageFrequency: JSON
  dosageQuantity: Float
  dosageUnit: String
  drugForm: String
  drugName: String
  drugPrice: Float
  drugPriceMarkUp: Float
  markedUpDrugPrice: Float
  notes: String
  priceListId: Float
  quantity: Float
  unitPrice: Float
}

type DrugsApprovals {
  _id: String
  consultation: String
  createdAt: DateTime
  doctor: String
  drugs: [Drug!]
  note: String
  partner: String
  patient: String
  providerId: String
  referral: String
  status: String
  updatedAt: DateTime
}

type DrugsApprovalsPayload {
  data: [PopulatedDrugsApprovals!]
  pageInfo: PageInfo
}

input DrugsInput {
  drugName: String
  drugPrice: Float
  drugPriceMarkUp: Float
  markedUpDrugPrice: Float
  notes: String
  paid: Boolean
  priceListId: Float
  quantity: Float
  unitPrice: Float
}

type Earning {
  _id: ID!
  balance: Float
  consultationId: Consultation
  createdAt: DateTime
  disputed: Boolean
  doctor: DoctorProfile
  providerId: Provider
  updatedAt: DateTime
}

type EarningConnection {
  data: [Earning!]!
  errors: [ErrorPayload!]
  pageInfo: PageInfo
  totalEarnings: Float
  totalPayouts: Float
}

type EarningPayload {
  earning: Earning
  errors: [ErrorPayload!]
}

type EarningsStat {
  earningData: JSON!
  payoutData: JSON!
  subscriptionIncome: Int!
  subscriptionIncomeData: JSON!
  totalEarnings: Int!
  totalPayout: Int!
}

input EditEmployeeInput {
  dob: String
  email: String
  firstName: String
  gender: String
  lastName: String
  noofdependants: Float
  phoneNumber: String
  type: String
}

"""Email custom scalar type"""
scalar EmailAddress

type EmailOutput {
  _id: ID!
  createdAt: DateTime
  email: EmailAddress
  profileData: JSONObject
  role: String
  updatedAt: DateTime
}

type Employee {
  _id: String
  admin: Profile
  businessId: Business
  createdAt: DateTime
  dependant: Boolean
  dependants: [Dependant!]
  dob: String
  email: String
  firstName: String
  gender: String
  lastName: String
  noofdependants: Float
  phoneNumber: String
  profileId: Profile
  relationship: String
  status: String
  type: String
  updatedAt: DateTime
}

type EmployeeDependants {
  employees: [Employee!]
}

input EmployeeInput {
  employeeId: String!
}

input EndConsultationInput {
  doctorEndCommunicationReason: String
  id: ID!
  messages: [ConsultationChatMessageInput!]
  patientEndCommunicationReason: String
}

type Enrollee {
  _id: ID!
  accessPlanName: String
  client: String
  companyId: Company
  deactivated: Boolean
  email: String
  expiryDate: DateTime
  firstName: String
  hmoId: String
  lastName: String
  noc: Int
  phone: String
  photo: String
  plan: String
  planId: Plan
  providerId: String
  status: Boolean
}

type EnrolleeDeactivatedPayload {
  count: Float!
  errors: [ErrorPayload!]!
}

input EnrolleeOrderInput {
  consultationId: String!
  deliveryOption: String!
  doctor: String!
  drugIds: [String!]!
  note: String
  patient: String!
  pharmacyAddress: String
  pharmacyCode: String
  pharmacyName: String
  prescriptionDate: String
  userLocation: UserLocationInput
}

type EnrolleePayload {
  enrollee: Enrollee
  message: String!
}

type EnrolleesConnection {
  data: [Enrollee!]!
  pageInfo: PageInfo!
}

type ErrorPayload {
  field: String!
  message: [String!]!
}

type ExportDetails {
  fileUrl: String
}

type ExternalPlan {
  _id: String
  allowedFeatures: AllowedFeatures
  amount: Float
  benefits: [BenefitPayload!]
  billingDayOffset: String
  code: String
  createdAt: DateTime!
  description: String
  duration: String
  external: Boolean
  externalProviderName: String
  image: String
  name: String
  noOfDoctors: Float
  planCode: String
  provider: String
  specialisation: String
  status: String
  subscribed: Boolean
  type: String
  updatedAt: DateTime!
}

input ExternalPlanInput {
  id: String!
}

input ExternalReference {
  doctor_email: String!
  doctor_institution: String!
  doctor_name: String!
  doctor_position: String!
}

type Family {
  _id: ID!
  admin: PatientProfile
  createdAt: DateTime
  dob: String
  email: String
  firstName: String
  gender: String
  image: String
  isActive: Boolean
  lastName: String
  phoneNumber: String
  profileId: PatientProfile
  relationship: String
  status: String
  token: String!
  tokenExpiresAt: String!
  updatedAt: DateTime
}

type FamilyArrayPayload {
  family: [Family!]!
  message: String!
  pageInfo: PageInfo!
}

input FamilyInput {
  id: String!
}

type FamilyPayload {
  family: Family
  message: String
}

type FavouriteDoctor {
  _id: ID!
  createdAt: DateTime!
  doctor: JSON
  patient: String!
  updatedAt: DateTime!
}

input FavouriteDoctorInput {
  doctor: String!
  patient: String!
}

type FavouriteDoctorPayload {
  favouriteDoctor: FavouriteDoctor!
}

type FavouriteDoctorsConnection {
  data: [FavouriteDoctor!]!
  pageInfo: PageInfo!
}

type FcmNotificationPayload {
  errors: [ErrorPayload!]
  notification: FcmNotificationResponse
}

type FcmNotificationResponse {
  failedTokens: [String!]
  failureCount: String
  multicastId: String
  notificationId: String
  successCount: String
}

type FcmToken {
  _id: ID
  createdAt: String
  deviceId: String
  role: String
  token: String
  updatedAt: String
  user: String
}

input FilterInputType {
  after: String
  before: String
  first: Float
  last: Float
  orderBy: String
  page: Float
  search: String
}

input FilterPlansInput {
  endMonth: Float
  planId: String!
  startMonth: Float
  year: Float
}

input FilterRegistrationsInput {
  endMonth: Float
  providerId: String
  startMonth: Float
  year: Float
}

input FinalizeInput {
  id: String!
  ids: [String!]
  partner: String!
}

input FinalizeSpecialistInput {
  id: String!
  ids: [String!]
}

input FindProfilesIdsInput {
  ids: [String!]!
}

input GenerateConsultationStatsInput {
  email: String!
  endDate: DateTime
  providerId: String!
  startDate: DateTime
}

input GenerateHmoIdInput {
  email: String!
  externalProviderId: Float!
}

input GenerateHmoInput {
  email: String!
}

input GetBusinessEmployeesInput {
  businessId: String!
}

input GetBusinessInput {
  businessId: String!
}

input GetDiagnosticDashboardInput {
  partner: String!
}

input GetFeaturedPartnerInput {
  category: String!
}

input GetPartnerConfigurationInput {
  category: String
  partner: String!
}

input GetPartnerSubdomainInput {
  subdomain: String!
}

input GetPharmacyDashboardInput {
  partner: String!
}

input GetReferralInput {
  id: String!
}

input GetSubscriptionInput {
  email: String
}

input GetVerificationInput {
  id: String!
}

input HmoIdInput {
  hmoId: String!
}

type HmoPlanType {
  name: String
  planId: String
}

type HmoPlans {
  name: String
  planId: String
}

input HmoPlansInput {
  name: String
  planId: String
}

type HospitalDrug {
  _id: String
  drugName: String
  drugPrice: Float
  notes: String
  partner: String
  priceListId: Float
  quantity: String
  unitPrice: Float
}

type HospitalMedication {
  _id: ID!
  cancellationReason: String
  consultationId: String
  createdAt: DateTime!
  deliveryFee: Float
  deliveryOption: String!
  doctor: DoctorProfile
  drugs: [HospitalDrug!]!
  note: String
  orderId: String!
  partner: Partner
  patient: Profile
  pharmacyCode: String
  status: String!
  total: Float
  updatedAt: DateTime!
  userLocation: Location!
}

type HospitalMedicationPayload {
  errors: [ErrorPayload!]!
  medication: HospitalMedication!
}

type HospitalMedicationsConnection {
  data: [HospitalMedication!]!
  pageInfo: PageInfo!
}

input IdInput {
  id: String!
}

type IdPastIllnessType {
  id: PastIllnessType
}

type Illness {
  _id: ID!
  createdAt: DateTimeScalar
  description: String
  name: String!
  updatedAt: DateTimeScalar
}

type IllnessConnection {
  data: [Illness!]!
  pageInfo: PageInfo!
}

input IllnessInput {
  id: String!
}

type IllnessOutput {
  _id: ID!
  createdAt: DateTimeScalar
  description: String
  name: String!
  updatedAt: DateTimeScalar
}

input InitRtcInput {
  consultationId: String!
}

type InitRtcPayload {
  rtcToken: String!
  uid: Int!
}

input InitializeDoctorInput {
  doctors: [String!]!
}

type IssueConnection {
  issue: Symptoms!
}

type IssueObj {
  Accuracy: String
  ID: String
  Icd: String
  IcdName: String
  Name: String
  ProfName: String
  Ranking: String
}

type IssuesConnection {
  issues: [Symptoms!]!
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON

"""JSONObject custom scalar type"""
scalar JSONObject

type Lab {
  _id: String
  createdAt: DateTime
  doctor: DoctorProfile
  partner: Partner
  patient: Profile
  updatedAt: DateTime
  url: String
}

type LabArrayPayload {
  data: [Lab!]
  errors: [ErrorPayload!]
  message: String
  pageInfo: PageInfo
}

type LabInfo {
  name: String
}

input LabInfoInput {
  name: String!
}

type LabResultPayload {
  errors: [ErrorPayload!]
  lab: Lab
  message: String
}

input License {
  expiry_date: DateTime!
  image: String!
  number: String!
  type: String!
}

type Location {
  address: String
  city: String
  landmark: String
  lat: Float
  lga: String
  lng: Float
  phoneNumber: String
  state: String
}

type LoginPayload {
  account: Account!
  message: String!
}

input LoginSocialDto {
  authType: String!
  token: String!
}

input LoginSocialInput {
  authType: String!
  deviceId: String!
  email: String!
}

input LoginUserInput {
  authType: String!
  email: String!
  password: String!
}

input MarkNotificationReadInput {
  id: String!
  user: String!
}

type MedicationsArrayPayload {
  medication: [PatientMedication!]
  message: String
  pageInfo: PageInfo
}

type MedicationsPayload {
  medication: PatientMedication
  message: String
}

type Message {
  _id: String
  body: String
  createdAt: DateTime
  recipient: Profile
  sender: String
  subject: String
  updatedAt: DateTime
}

type MessageConnection {
  messages: [Message!]!
  pageInfo: PageInfo!
}

type MessagePayload {
  messages: Message
}

input MessagingConsultationInput {
  consultationId: String!
}

input MessagingInput {
  id: String!
}

type MultipleDoctorProfileArrayPayload {
  errors: [ErrorPayload!]
  profiles: [DoctorProfile!]
}

input MultipleDoctorProfileInput {
  ids: [String!]!
}

type Mutation {
  acceptConsultation(data: AcceptConsultationInput!): ConsultationPayload!
  acceptInvite: Employee!
  acceptRenewal(data: VerifyHcpInput!): Verification!
  accountsNeverConsulted: Boolean!
  addBankAccount(data: AddBankAccountInput!): DoctorProfilePayload!
  addDependants(data: AddDependants!): EmployeeDependants!
  addDiagnosticLabTest(data: DiagnosticLabTestInput!): DiagnosticLabTestPayload!
  addDiagnosticReferral(data: AddDiagnosticReferralInput!): DiagnosticTestPayload!
  addDoctorConsultation(data: AddDoctorConsultationInput!): ConsultationPayload!
  addDrugOrder(data: AddDrugOrderInput!): DrugOrderPayload!
  addHospitalMedication(data: AddHospitalMedicationInput!): HospitalMedicationPayload!
  addPartner(data: AddPartnerInput!): PartnerPayload!
  addPartnerCategory(data: PartnerCategoryInput!): PartnerCategoryPayload!
  addPastIllness(data: UpdatePatientIllnessInput!): CreateProfile!
  addPrescriptions(data: AddPrescriptionsInput!): Prescription!
  addSpecialization(data: AddSpecializationInput!): Specialization!
  bulkUpdateReferralDrugs(data: BulkUpdateInput!): PrescriptionReferral!
  bulkUpdateReferralTests(data: BulkUpdateInput!): PrescriptionReferral!
  cancelConsultation(data: CancelConsultationInput!): ConsultationPayload!
  cancelDiagnosticTest(data: CancelDiagnosticTestInput!): DiagnosticTestPayload!
  cancelDispute(data: CancelDisputeInput!): ConsultationPayload!
  cancelDrugOrder(data: CancelDrugOrderInput!): DrugOrderPayload!
  changeDrugApprovalStatus(data: ChangeApprovalStatus!): DrugsApprovals!
  changeTestApprovalStatus(data: ChangeApprovalStatus!): TestsApprovals!
  completeDiagnosticTest(data: CompleteDiagnosticTestInput!): DiagnosticTestPayload!
  completePasswordReset(data: CompletePasswordInput!): AccountPayload!
  confirmDoctorPatientInvite(data: ConfirmPatientInviteInput!): DoctorPatientPayload!
  confirmEmployeeInvite(data: ConfirmEmployeeInput!): Employee!
  confirmInvite(data: ConfirmInviteInput!): FamilyPayload!
  createAllergy(data: CreateAllergyInput!): AllergyPayload!
  createAppointment(data: CreateAppointmentInput!): AppointmentPayload!
  createAvailabileTime(data: AvailableTimeInput!): AvailableTimePayload!
  createBusiness(data: CreateBusinessInput!): CreateBusinessPayload!
  createCompany(data: CreateCompanyInput!): CompanyPayload!
  createConsultation(data: CreateConsultationInput!): ConsultationPayload!
  createCurrentIllness(data: CreateCurrentIllnessInput!): CreateCurrentIllness!
  createDispute(data: CreateDisputeInput!): ConsultationPayload!
  createDoctorAccount(data: CreateDoctorProfileAccountInput!): DoctorProfilePayload!
  createDoctorProfile(data: CreateDoctorProfileInput!): DoctorProfilePayload!
  createEarning(data: CreateEarningInput!): EarningPayload!
  createEnrollee(data: CreateEnrolleeInput!): EnrolleePayload!
  createEnterprise(data: CreateEnterpriseInput!): CreateBusinessPayload!
  createExternalPlan(data: CreateInsurancePlanInput!): ExternalPlan!
  createIllness(data: CreateIllnessInput!): IllnessOutput!
  createLabResult(data: CreateLabResultInput!): LabResultPayload!
  createMedication(data: CreatePatientMedicationInput!): MedicationsPayload!
  createMessage(data: CreateMessageInput!): MessagePayload!
  createPatientAccount(data: CreatePatientProfileInput!): CreateProfile!
  createPayout(data: CreatePayoutInput!): PayoutPayload!
  createPermission(data: CreatePermissionInput!): PermissionPayload!
  createPlan(data: CreatePlanInput!): PopulatedPlan!
  createPrescriptionReferral(data: AddPrescriptionReferralInput!): PrescriptionReferral!
  createPriority(data: Priority!): DoctorThreshold!
  createProfile(data: CreateProfileInput!): CreateProfile!
  createProvider(data: CreateProviderInput!): ProviderPayload!
  createQuote(data: AddQuoteInput!): Quote!
  createReminder(data: CreateReminderInput!): ReminderPayload!
  createRequest(data: CreateRequestInput!): RequestPayload!
  createRole(data: CreateRoleInput!): Role!
  createSetting(data: CreateSettingInput!): SettingPayload!
  createTicket(data: CreateTicketInput!): TicketPayload!
  createUserType(data: CreateUserTypeInput!): UserTypePayload!
  createVerification(data: CreateVerificationInput!): VerificationPayload!
  deactivateCompanyEnrollees(data: DeactivateCompanyEnrolleeInput!): EnrolleeDeactivatedPayload!
  deactivateEnrollee(data: DeactivateEnrolleeInput!): EnrolleePayload!
  declineConsultation(data: DeclineConsultationInput!): ConsultationPayload!
  deleteAccount: Int!
  deleteAllergy(data: DeleteAllergyInput!): Int!
  deleteAppointment(data: AppointmentInput!): Int!
  deleteAvailableTime(data: AvailableIdInput!): DeleteAvailableTimePayload!
  deleteCompany(data: CompanyInput!): Int!
  deleteConsultation(data: ConsultationId!): DeleteConsultationPayload!
  deleteDoctorAvailability(data: AvailableIdInput!): Int!
  deleteDoctorPatient(data: DoctorPatientIdInput!): DeleteDoctorPatientPayload!
  deleteDoctorProfile(data: DeleteProfileInput!): Int!
  deleteEnrollee(data: DeleteEnrolleeInput!): Int!
  deleteIllness(data: IllnessInput!): Int!
  deleteLabResult(data: DeleteLabInput!): Int!
  deleteMedication(data: PatientMedicationInput!): Int!
  deleteMember(data: DeleteFamilyInput!): Int!
  deleteMyAccount(data: DeleteMyAccountInput!): Boolean!
  deletePermission(data: PermissionInput!): DeletePermissionPayload!
  deletePlan(data: DeletePlanInput!): Int!
  deleteProfile(data: DoctorProfileInput!): Int!
  deleteProvider(data: DeleteProviderInput!): DeleteProviderPayload!
  deleteReminder(data: ReminderInput!): Int!
  deleteRole(data: DeleteRoleInput!): Int!
  deleteSetting(data: DeleteSettingInput!): DeleteSettingPayload!
  deleteSpecialization(data: IdInput!): Boolean!
  deleteUserType(data: DeleteUserTypeInput!): Int!
  disableAccount(data: DisableAccountInput!): Boolean!
  editDependant(data: RemoveDependantInput!): Employee!
  editEmployee(data: EditEmployeeInput!): Employee!
  editMember(data: CreateFamilyInput!): FamilyPayload!
  endCommunication(data: EndConsultationInput!): ConsultationPayload!
  endConsultation(data: ConsultationId!): ConsultationCombinedPayload!
  enrolleeDrugOrder(data: EnrolleeOrderInput!): DrugOrderPayload!
  failConsultation(data: ConsultationId!): DeleteConsultationPayload!
  favouriteDoctor(data: FavouriteDoctorInput!): FavouriteDoctorPayload!
  finalizeReferralDrugs(data: FinalizeInput!): PrescriptionReferral!
  finalizeReferralSpecialist(data: FinalizeSpecialistInput!): PrescriptionReferral!
  finalizeReferralTests(data: FinalizeInput!): PrescriptionReferral!
  fulfillDrugOrder(data: DrugOrderInput!): DrugOrderPayload!
  generateAccountApiKey: ApikeyPayload!
  generateEnrollee(data: GenerateHmoInput!): JSON!
  generateNemEnrollee(data: GenerateHmoIdInput!): JSON!
  getQuote(data: QuoteInput!): Quote!
  initPayment(data: PaymentInitInput!): PaymentInitPayload!
  initRtc(data: InitRtcInput!): InitRtcPayload!
  initializeDoctor(data: InitializeDoctorInput!): PayoutsListConnection!
  initializePayout(data: CreatePayoutsInput!): PayoutsListConnection!
  inviteDoctorPatient(data: CreateDoctorPatientInput!): DoctorPatientPayload!
  inviteEmployee(data: AddEmployeeInput!): Employee!
  inviteMember(data: CreateFamilyInput!): FamilyPayload!
  login(data: LoginUserInput!): LoginPayload!
  loginSocial(data: LoginSocialDto!): LoginPayload!
  logout: Boolean!
  markMessagesRead(data: MessagingInput!): ChatMessagesPayload!
  markNotificationRead(data: MarkNotificationReadInput!): Notification!
  markNotificationScheduleNotified(data: ConsultationId!): JSON!
  overrideEnrollees(data: UploadEnrolleesInput!): Boolean!
  rateConsultation(data: AddRatingInput!): ConsultationPayload!
  reactivateCompanyEnrollees(data: DeactivateCompanyEnrolleeInput!): EnrolleeDeactivatedPayload!
  rebroadcastConsultationNotification(data: RebroadCastNotificationInput!): ConsultationPayload!
  refreshToken: RefreshPayload!
  regenerateProviderProfileUrl(data: UpdateRegenerateInput!): PopulatedProviderPayload!
  registerBusiness(data: RegisterBusinessInput!): RegisterBusinessPayload!
  registerFamily(data: RegisterFamilyInput!): RegisterFamilyPayload!
  registerMicroInsuranceIndividual(data: RegisterMicroInsuranceIndividualInput!): RegisterMicroInsuranceIndividualPayload!
  rejectLicenseRenewal(data: RejectVerificationInput!): RejectionVerificationPayload!
  rejectVerification(data: RejectVerificationInput!): RejectionVerificationPayload!
  removeBusiness(id: Int!): Business!
  removeDependant(data: RemoveDependantInput!): Boolean!
  removeEmployee(data: EmployeeInput!): Boolean!
  renewLicense(data: RenewLicenseInput!): VerificationPayload!
  requestReferral(data: RequestReferralInput!): ReferralPayload!
  rescheduleConsultation(data: RescheduleConsultationInput!): ConsultationPayload!
  resendEnrolleeData(data: ConsultationId!): ConsultationCombinedPayload!
  resendInvite(data: ResendEmployeeInput!): Boolean!
  resendOTP(data: ResendInput!): Boolean!
  resetPassword(data: ResetInput!): Boolean!
  resolveDispute(data: ResolveDisputeInput!): ConsultationPayload!
  restoreAccount(data: RestoreMyAccountInput!): AccountData!
  scheduleDiagnosticTest(data: AddDiagnosticTestInput!): DiagnosticTestPayload!
  sendChatMessage(data: ChatMessageInput!): ChatMessagePayload!
  sendConsultation(data: ConsultationId!): ConsultationPayload!
  sendNotification(data: SendNotificationsInput!): FcmNotificationPayload!
  sendOTP(data: ResendInput!): Boolean!
  setAvailability(data: SetAvailabilityInput!): AvailabilityPayload!
  setEmployeePermanentPassword(data: SetPermanentPassword!): AccountPayload!
  setPermanentPassword(data: SetPermanentPassword!): AccountPayload!
  signup(data: SignupUserInput!): SignupPayload!
  socialLogin(data: LoginSocialInput!): LoginPayload!
  socialSignup(data: SocialSignupUserInput!): SignupPayload!
  startCommunication(data: ConsultationId!): ConsultationPayload!
  testDate: JSON!
  unFavouriteDoctor(data: FavouriteDoctorInput!): Int!
  updateAllergy(data: UpdateAllergyInput!): AllergyPayload!
  updateAppointment(data: UpdateAppointmentInput!): AppointmentPayload!
  updateAvailableTime(data: UpdateAvailableTimeInput!): AvailableTimePayload!
  updateBusiness(updateBusinessInput: UpdateBusinessInput!): Business!
  updateCompany(data: UpdateCompanyInput!): CompanyPayload!
  updateConsultation(data: UpdateConsultationInput!): ConsultationPayload!
  updateDiagnosticLabTest(data: UpdateDiagnosticLabTestInput!): DiagnosticLabTestPayload!
  updateDoctorPatient(data: UpdateDoctorPatient!): DoctorPatientPayload!
  updateDoctorProfile(data: UpdateDoctorProfileInput!): DoctorProfilePayload!
  updateDoctorProvider(data: UpdateDoctorProviderInput!): UpdateDoctorProviderPayload!
  updateDrugOrder(data: UpdateDrugOrderInput!): DrugOrderPayload!
  updateEmail(data: UpdateEmailInput!): AccountData!
  updateEmployee(data: UpdateEmployeeInput!): Employee!
  updateEnrollee(data: UpdateEnrolleeInput!): EnrolleePayload!
  updateExternalPlan(data: UpdateInsurancePlanInput!): ExternalPlan!
  updateFamily(data: UpdateFamilyInput!): FamilyPayload!
  updateFcmToken(data: UpdateFcmTokenInput!): FcmToken!
  updateHospitalMedication(data: UpdateHospitalMedicationInput!): HospitalMedicationPayload!
  updateIllness(data: UpdateIllnessInput!): IllnessOutput!
  updateJoinedConsultation(data: UpdateJoinedInput!): ConsultationPayload!
  updateLabResult(data: UpdateLabInput!): LabResultPayload!
  updateLicense(data: CreateVerificationInput!): VerificationPayload!
  updateMedication(data: UpdatePatientMedicationInput!): MedicationsPayload!
  updatePartnerConfiguration(data: PartnerConfigurationInput!): PartnerConfiguration!
  updatePartnerProfile(data: UpdatePartnerInput!): PartnerPayload!
  updatePartnerSubdomain(data: PartnerSubdomainInput!): PartnerConfiguration!
  updatePassword(data: UpdatePasswordInput!): AccountData!
  updatePermission(data: UpdatePermissionInput!): PermissionPayload!
  updatePharmacyDrug(data: UpdatePharmacyDrugInput!): UpdatePharmacyDrugPayload!
  updatePlan(data: UpdatePlanInput!): PopulatedPlan!
  updatePlanOnPayStack(data: UpdatePayStackPlanInput!): ExternalPlan!
  updatePrescriptionReferral(data: UpdatePrescriptionReferralInput!): PrescriptionReferral!
  updatePriority(data: UpdatePriority!): DoctorThreshold!
  updateProfile(data: UpdateProfileInput!): CreateProfile!
  updateProvider(data: UpdateProviderInput!): PopulatedProviderPayload!
  updateQuote(data: QuoteStatusInput!): Quote!
  updateReferral(data: UpdateReferralInput!): ReferralPayload!
  updateReferralDrug(data: UpdateDrugInput!): PrescriptionReferral!
  updateReferralTest(data: UpdateTestInput!): PrescriptionReferral!
  updateReminder(data: UpdateReminderInput!): ReminderPayload!
  updateRole(data: UpdateRoleInput!): Role!
  updateSatisfaction(data: UpdateSatisFactionInput!): ConsultationPayload!
  updateSetting(data: UpdateSettingInput!): SettingPayload!
  updateSpecialization(data: UpdateSpecializationInput!): Specialization!
  updateUserProvider(data: UpdateUserProviderInput!): AccountProfile!
  updateUserType(data: UpdateUserTypeInput!): UserTypePayload!
  uploadChatMessages(data: ChatMessagesUploadInput!): ChatMessagesPayload!
  uploadDiagnosticLabTests(data: UploadLabTestsInput!): UploadLabTestsPayload!
  uploadEmployees(data: UploadEmployeesInput!): UploadEmployeesPayload!
  uploadEnrollees(data: UploadEnrolleesInput!): UploadEnrolleesPayload!
  uploadPharmacyDrugs(data: UploadPharmacyDrugsInput!): UploadPharmacyDrugsPayload!
  validateEnrollee(data: ValidateEnrolleeInput!): EnrolleePayload!
  verifyEmail(data: VerifyEmailInput!): VerifyEmailPayload!
  verifyHCP(data: VerifyHcpInput!): Verification!
}

input NewsLetterContentInput {
  content: String
  title: String
}

input NewsLetterInput {
  body: [NewsLetterContentInput!]!
  heading: String!
  to: [String!]!
}

type Notification {
  _id: ID
  content: String
  createdAt: DateTime
  itemId: String
  previewImageUri: String
  previewImageUriThumbnail: String
  role: String
  saveNotification: Boolean
  seen: Boolean
  tag: String
  ticker: String
  title: String
  updatedAt: DateTime
  useSound: String
  user: String
}

type NotificationSchedule {
  _id: ID
  consultation: Consultation
  consultationId: String
  notified: Boolean
  time: String
}

type NotificationsConnection {
  data: [Notification!]!
}

type PageInfo {
  hasNextPage: Boolean
  hasPrevPage: Boolean
  limit: Int
  nextPage: Int
  offset: Int
  page: Int
  pagingCounter: Int
  prevPage: Int
  totalDocs: Int
  totalPages: Int
}

type Partner {
  _id: ID!
  accountId: String
  address: String
  bankDetails: [BankDetails!]
  category: String
  classification: String
  dociId: String
  email: String
  logoImageUrl: String
  name: String
  phone: String
  profileUrl: String
  providerId: String
  specialisation: String
}

type PartnerCategoriesConnection {
  data: [PartnerCategory!]!
}

type PartnerCategory {
  _id: ID!
  createdAt: DateTime!
  name: String!
  updatedAt: DateTime!
}

input PartnerCategoryInput {
  name: String!
}

type PartnerCategoryPayload {
  category: PartnerCategory!
  errors: [ErrorPayload!]!
}

type PartnerConfiguration {
  apiKey: String
  category: String
  drugDeliveryFee: Float
  drugPriceMarkUp: Float
  partner: String
  providerId: String
  subdomain: String
  testPickUpFee: Float
  testPickUpFeeLimit: Float
  testPriceMarkup: Float
  widgetColor: String
  widgetIcon: String
  widgetLogo: String
  widgetPosition: String
  widgetSize: String
  widgetTextColor: String
  widgetTextHeader: String
}

input PartnerConfigurationInput {
  category: String
  drugDeliveryFee: Float
  drugPriceMarkUp: Float
  partner: String!
  subdomain: String
  testPickUpFee: Float
  testPickUpFeeLimit: Float
  testPriceMarkup: Float
  widgetColor: String
  widgetIcon: String
  widgetLogo: String
  widgetPosition: String
  widgetSize: String
  widgetTextColor: String
  widgetTextHeader: String
}

input PartnerInput {
  id: String!
}

type PartnerPayload {
  errors: [ErrorPayload!]!
  partner: Partner!
}

input PartnerSubdomainInput {
  partner: String!
  subdomain: String!
}

type PartnersConnection {
  data: [Partner!]!
  pageInfo: PageInfo!
}

type PastIllnessType {
  _id: String
  description: String
  name: String
}

type PastIllnessTypes {
  description: String
  id: String
  name: String
}

type PatientMedication {
  _id: String
  createdAt: DateTime
  doctor: ID!
  dosage: Float!
  interval: String
  name: String!
  patient: ID!
  updatedAt: DateTime
}

input PatientMedicationInput {
  id: String!
}

type PatientProfile {
  _id: String
  accountId: String
  bloodGroup: String
  consultations: Float
  createdAt: DateTime
  dob: DateTimeScalar
  dociId: String
  email: String
  firstName: String
  gender: String
  genotype: String
  height: Float
  hmoId: String
  image: String
  lastName: String
  pastIllness: [PastIllnessTypes!]
  phoneNumber: String
  plan: String
  providerId: String
  rating: Float
  status: String
  timezoneOffset: String
  updatedAt: DateTime
  weight: Float
}

input PaymentInitInput {
  amount: Float
  callback_url: String
  email: String!
  itemId: String!
  noOfUsers: Float
  plan: String
  reason: String!
  saveCard: Boolean
  scope: String
  type: String
  user: String!
}

type PaymentInitPayload {
  errors: [ErrorPayload!]
  paymentInitResponse: PaymentInitResponse!
}

type PaymentInitResponse {
  authorization_url: String!
  reference: String!
}

type Payout {
  _id: ID!
  amount: Float
  createdAt: DateTime
  doctor: DoctorProfile
  providerId: Provider
  status: String
  updatedAt: DateTime
}

type PayoutConnection {
  data: [Payout!]!
  errors: [ErrorPayload!]
  pageInfo: PageInfo
}

type PayoutPayload {
  errors: [ErrorPayload!]
  payout: Payout
}

type PayoutsListConnection {
  data: [Payout!]
}

type PaystackAuthorization {
  account_name: String
  authorization_code: String
  bank: String
  bin: String
  brand: String
  card_type: String
  channel: String
  country_code: String
  exp_month: String
  exp_year: String
  last4: String
  reusable: String
  signature: String
}

type Permission {
  _id: ID
  createdAt: DateTime
  description: String
  name: String!
  updatedAt: DateTime
}

type PermissionConnection {
  errors: [ErrorPayload!]
  pageInfo: PageInfo!
  permission: [Permission!]!
}

input PermissionInput {
  id: String!
}

type PermissionPayload {
  errors: [ErrorPayload!]!
  permission: Permission
}

type PharmacyDashboard {
  cancelledDrugOrdersCount: Float
  cancelledDrugOrdersStats: [DiagnosticCalendarPayload!]
  completedDrugOrdersCount: Float
  completedDrugOrdersStats: [DiagnosticCalendarPayload!]
  drugOrderRequestsCount: Float
  drugOrderRequestsStats: [DiagnosticCalendarPayload!]
  processingDrugOrdersCount: Float
  processingDrugOrdersStats: [DiagnosticCalendarPayload!]
}

type PharmacyDrug {
  _id: ID
  drugName: String
  drugPrice: Float
  notes: String
  partner: String
  priceListId: Float
  quantity: String
  unitPrice: Float
}

type PharmacyDrugsConnection {
  data: [PharmacyDrug!]!
}

type Plan {
  _id: ID
  allowedFeatures: JSONObject
  amount: Float
  createdAt: DateTimeScalar
  description: String
  duration: String
  name: String!
  provider: String
  providerData: JSONObject
  status: String
  subscribed: Boolean
  type: String
  updatedAt: DateTimeScalar
}

input PlanCodeInput {
  planCode: String!
}

type PlanPayload {
  _id: String
  allowedFeatures: AllowedFeatures
  amount: Float
  benefits: [Benefit!]
  billingDayOffset: String
  code: String
  createdAt: DateTime!
  description: String
  duration: String
  external: Boolean
  externalProviderName: Boolean
  image: String
  monthlyAmount: Float
  name: String
  noOfDoctors: Float
  planCode: String
  provider: Provider
  specialisation: String
  status: String
  subscribed: Boolean
  type: String
  updatedAt: DateTime!
  yearlyAmount: Float
}

type PlanType {
  _id: ID
  allowedFeatures: ConsultType
  amount: Float
  billingDayOffset: Int
  description: String
  duration: String
  name: String
  planCode: String
  provider: String
  type: String
}

type PlansPayload {
  data: [PlanPayload!]
  pageInfo: PageInfo
}

type PopulatedAccount {
  _id: ID
  access_token: String
  apiKey: String
  authType: String
  createdAt: DateTime
  deactivateType: String
  deactivated: Boolean
  deactivatedAt: String
  dociId: String
  email: String
  enrolleeNumber: String
  isActive: Boolean
  isEmailVerified: Boolean
  isPasswordTemporary: Boolean
  nextStep: String
  otpTokenExpiresAt: String
  providerId: ID
  refresh_token: String
  role: String
  updatedAt: DateTime
  userTypeId: UserAccountType
}

type PopulatedBusiness {
  _id: String
  address: String
  category: String
  createdAt: DateTime
  email: String
  image: String
  industry: String
  name: String
  noOfEmployees: Float
  profileId: Profile
  providerId: ProviderType
  type: String
  updatedAt: DateTime
  userTypeId: UserAccountType
}

type PopulatedBusinessPayload {
  data: [PopulatedBusiness!]
  pageInfo: PageInfo
}

type PopulatedDrugsApprovals {
  _id: String
  consultation: UnPopulatedConsultation
  createdAt: DateTime
  doctor: UnPopulatedDoctorProfile
  drugs: [Drug!]
  note: String
  partner: Partner
  patient: Profile
  providerId: ProviderType
  referral: PrescriptionReferral
  status: String
  updatedAt: DateTime
}

type PopulatedEmployeePayload {
  data: [Employee!]
  pageInfo: PageInfo
}

type PopulatedPlan {
  _id: String!
  allowedFeatures: AllowedFeatures
  amount: Float!
  billingDayOffset: String
  createdAt: DateTime!
  description: String
  duration: String
  image: String
  name: String!
  noOfDoctors: Float
  planCode: String
  provider: String
  specialisation: String
  type: String
  updatedAt: DateTime!
}

type PopulatedPrescriptionReferral {
  _id: String
  consultation: UnPopulatedConsultation
  createdAt: DateTime
  doctor: UnPopulatedDoctorProfile
  drugStatus: String
  drugs: [Drug!]
  drugsNote: String
  drugsPartner: Partner
  patient: Profile
  providerId: ProviderType
  specialistStatus: String
  specialists: [Specialist!]
  testStatus: String
  tests: [Test!]
  testsNote: String
  testsPartner: Partner
  updatedAt: DateTime
}

type PopulatedProfile {
  _id: ID
  accountId: Account
  address: String
  bloodGroup: String
  consultations: Int
  createdAt: DateTimeScalar
  dob: DateTime
  dociId: String
  email: String
  externalPlanCode: String
  externalPlanType: String
  externalProvider: String
  externalProviderId: String
  firstName: String
  gender: String
  genotype: String
  height: Float
  hmoId: String
  image: String
  isNemEnrollee: Boolean
  isPrincipal: Boolean
  lastName: String
  noOfDependant: Float
  pastIllness: [IdPastIllnessType!]
  phoneNumber: String
  plan: String
  providerId: ProviderType
  rating: Int
  status: String
  subscriptionId: SubscriptionType
  timezoneOffset: String
  updatedAt: DateTimeScalar
  weight: Float
}

type PopulatedProvider {
  _id: ID
  address: String
  createdAt: DateTimeScalar
  doctorCount: Int
  email: String
  enrolleeCount: Int
  hmoPlans: [HmoPlans!]
  icon: String
  iconAlt: String
  isWellaHealthIntegration: Boolean
  name: String!
  partnerCount: Int
  phone: String
  planId: String
  profileUrl: String
  rareCase: Boolean
  updatedAt: DateTimeScalar
  userCount: Int
  userTypeId: UserType
}

type PopulatedProviderPayload {
  message: String
  provider: PopulatedProvider
}

type PopulatedTestsApprovals {
  _id: String
  consultation: UnPopulatedConsultation
  createdAt: DateTime
  doctor: UnPopulatedDoctorProfile
  note: String
  partner: Partner
  patient: Profile
  providerId: ProviderType
  referral: PrescriptionReferral
  status: String
  tests: [Test!]
  updatedAt: DateTime
}

type Prescription {
  _id: ID!
  consultation: String
  createdAt: DateTime
  doctor: DoctorProfile
  drugs: [PrescriptionDrug!]
  patient: Profile
  updatedAt: DateTime
}

type PrescriptionDrug {
  amount: Float
  dosageFrequency: DosageFreq
  dosageQuantity: String
  dosageUnit: String
  drugForm: String
  drugName: String
  drugPrice: Float
  instructions: String
  notes: String
  paid: Boolean
  priceListId: Float
  quantity: Float
  route: String
  unitPrice: Float
}

input PrescriptionInput {
  amount: Float!
  dosageFrequency: DosageFrequency!
  dosageUnit: String!
  drugForm: String!
  drugName: String!
  drugPrice: Float!
  instructions: String
  notes: String
  paid: Boolean
  priceListId: Float!
  quantity: Float!
  route: String!
  unitPrice: Float!
}

type PrescriptionReferral {
  _id: String
  consultation: String
  createdAt: DateTime
  doctor: String
  drugStatus: String
  drugs: [Drug!]
  drugsNote: String
  drugsPartner: String
  patient: String
  providerId: String
  specialistStatus: String
  specialists: [Specialist!]
  testStatus: String
  tests: [Test!]
  testsNote: String
  testsPartner: String
  updatedAt: DateTime
}

input PrescriptionReferralInput {
  id: String!
}

type PrescriptionReferralPayload {
  data: [PopulatedPrescriptionReferral!]
  pageInfo: PageInfo
}

type PrescriptionsConnection {
  data: [Prescription!]
  pageInfo: PageInfo
}

input Priority {
  end: Float
  priority: Float!
  start: Float!
}

type Profile {
  _id: ID
  accountData: Account
  bloodGroup: String
  consultations: Int
  createdAt: DateTime
  dob: DateTime
  dociId: String
  email: String
  externalPlanCode: String
  externalPlanType: String
  externalProvider: String
  externalProviderId: String
  firstName: String
  gender: String
  genotype: String
  height: Float
  hmoId: String
  image: String
  lastName: String
  noOfDependant: Int
  pastIllness: [PastIllnessType!]
  phoneNumber: String
  plan: String
  provider: String
  providerId: String
  rating: Int
  status: String
  timezoneOffset: String
  updatedAt: DateTime
  weight: Float
}

type ProfileConnection {
  data: [PopulatedProfile!]!
  pageInfo: PageInfo!
}

type Provider {
  _id: ID
  address: String
  consultationLimitType: String
  createdAt: DateTime
  doctorCount: Int
  email: String
  enrolleeCount: Int
  hmoPlans: [HmoPlans!]
  icon: String
  iconAlt: String
  isWellaHealthIntegration: Boolean
  monthlyConsultationLimit: Int
  name: String!
  partnerCount: Int
  phone: String
  planId: String
  profileUrl: String
  rareCase: Boolean
  updatedAt: DateTime
  userCount: Int
  userTypeId: UserType
}

type ProviderPayload {
  message: String
  provider: Provider
}

type ProviderPopulatedType {
  _id: ID
  address: String
  consultationLimitType: String
  createdAt: DateTime
  doctorCount: Int
  email: String
  enrolleeCount: Int
  hmoPlans: [HmoPlans!]
  icon: String
  iconAlt: String
  isWellaHealthIntegration: Boolean
  monthlyConsultationLimit: Int
  name: String!
  partnerCount: Int
  phone: String
  planId: String
  profileUrl: String
  rareCase: Boolean
  totalPendingAmount: Int
  updatedAt: DateTime
  userCount: Int
  userTypeId: UserType
}

type ProviderType {
  _id: ID
  address: String
  createdAt: DateTime
  email: String
  hmoPlans: [HmoPlanType!]
  icon: String
  iconAlt: String
  name: String
  phone: String
  planId: String
  profileUrl: String
  rareCase: Boolean
  updatedAt: DateTime
  userTypeId: String
}

type ProviderTypeConnection {
  pageInfo: PageInfo
  provider: [ProviderPopulatedType!]
}

type ProvidersType {
  _id: ID
  address: String
  consultationLimitType: String
  createdAt: DateTime
  doctorCount: Int
  email: String
  enrolleeCount: Int
  hmoPlans: [HmoPlans!]
  icon: String
  iconAlt: String
  isWellaHealthIntegration: Boolean
  monthlyConsultationLimit: Int
  name: String!
  partnerCount: Int
  phone: String
  planId: String
  profileUrl: String
  rareCase: Boolean
  updatedAt: DateTime
  userCount: Int
  userTypeId: UserType
}

input Qualification {
  degree: String!
  image: String!
  year: DateTime!
}

type Query {
  PatientCount(filterBy: JSON, q: String): Int!
  accessConsultation(data: ConsultationId!): AccountPayload!
  accessDoctorConsultation(data: ConsultationId!): AccountPayload!
  account(id: String!): Account!
  accountCount(filterBy: JSON, q: String): Int!
  accounts(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): AccountConnection!
  activityLogs(limit: Int! = 10, skip: Int! = 0): [ActivityLog!]!
  allergies(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): AllergyArrayPayload!
  allergy(data: AllergyInput!): Allergy!
  business(id: Int!): Business!
  businesses(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): PopulatedBusinessPayload!
  cancelSubscription(data: UserPlanInput!): CancelSubscriptionConnection!
  checkActivityLogger: JSONObject!
  checkCharge(data: CheckChargeInput!): CheckChargeConnection!
  checkHasSubscription(data: UserPlanInput!): CheckHasSubscriptionConnection!
  checkNemEnrollee(data: GetSubscriptionInput!): JSON!
  countConsultation(data: ConsultationId!): Int!
  debugSentry: JSON!
  deleteDiagnosticLabTest(data: DiagnosticInput!): Int!
  deletePartner(data: PartnerInput!): Partner!
  doctorCount(data: DoctorProfileCountInput): Int!
  doctorProfile(data: DoctorProfileInput!): DoctorProfilePayload!
  doctorProfileByEmail(data: DoctorProfileInput!): DoctorProfilePayload!
  doctorProfiles(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, search: String): DoctorProfileArrayPayload!
  doctorProfilesByStatus(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, search: String): DoctorProfileArrayPayload!
  drugApproval(data: IdInput!): PopulatedDrugsApprovals!
  drugsApprovals(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, search: String): DrugsApprovalsPayload!
  employees(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): PopulatedEmployeePayload!
  exportConsultations(after: String, before: String, endDate: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, startDate: String): ExportDetails!
  exportConsultationsNew(after: String, before: String, endDate: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, startDate: String): Boolean!
  families(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): FamilyArrayPayload!
  findMultipleDoctorProfiles(data: MultipleDoctorProfileInput!): MultipleDoctorProfileArrayPayload!
  findMultipleProfiles(after: String, before: String, data: FindProfilesIdsInput!, first: Int, last: Int, orderBy: String, page: Int): ProfileConnection!
  generateConsultationsStat(data: GenerateConsultationStatsInput!): JSON!
  getAllAppointments(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): AppointmentConnection!
  getAllStats(filterBy: JSON): Stats!
  getAppointment(data: AppointmentInput!): Appointment!
  getAppointments(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): AppointmentConnection!
  getAvailabilities(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): AvailabilityConnection!
  getAvailability(data: AvailableIdInput!): Availability!
  getAvailableDoctors(after: String, before: String, date: String!, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): DoctorProfileArrayPayload!
  getAvailableTime(data: AvailableTimeIdInput!): AvailableTime!
  getAvailableTimes(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): AvailableTimeConnection!
  getBookedConsultationTimesForDoctor(data: BookedConsultationTimeInput!): BookedConsultationTimeConnection!
  getBusiness(data: GetBusinessInput!): Business!
  getBusinessEmployees(data: GetBusinessEmployeesInput!): UnPopulatedEmployeePayload!
  getCards(data: CardInput!): CardsConnections!
  getChatConversations: ChatConversationsConnection!
  getChatMessages(data: MessagingConsultationInput!): ChatMessagesPayload!
  getCompanies(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, search: String): CompanyConnection!
  getCompany(data: CompanyInput!): Company!
  getConsultation(data: ConsultationId!): Consultation!
  getConsultations(after: String, before: String, endDate: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, startDate: String): ConsultationConnection!
  getDiagnosis(gender: String!, symptoms: [Int!]!, year_of_birth: Float!): DiagnosisConnection!
  getDiagnosticDashboard(data: GetDiagnosticDashboardInput!): DiagnosticDashboard!
  getDiagnosticLabTest(data: DiagnosticInput!): DiagnosticLabTest!
  getDiagnosticLabTests(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): DiagnosticLabTestsConnection!
  getDiagnosticTest(data: DiagnosticInput!): DiagnosticTest!
  getDiagnosticTests(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): DiagnosticTestsConnection!
  getDoctorAvailability(data: AvailableIdInput!): AvailabilityConnection!
  getDoctorAvailabilityForDate(data: AvailableDoctorTimesInput!): Availability!
  getDoctorConsultationDetails(data: ConsultationId!): JSON!
  getDoctorPatient(data: DoctorPatientIdInput!): DoctorPatient!
  getDoctorPatients(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): DoctorPatientConnection!
  getDrugOrder(data: DrugOrderInput!): DrugOrder!
  getDrugOrders(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int): DrugOrdersConnection!
  getDueConsultationNotificationSchedules(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): JSON!
  getEarningStats(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): EarningsStat!
  getEmailList(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, search: String): AccountConnection!
  getEnrollee(data: DeleteEnrolleeInput!): Enrollee!
  getEnrollees(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, search: String): EnrolleesConnection!
  getExternalPlan(data: ExternalPlanInput!): ExternalPlan!
  getExternalPlans(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): PlansPayload!
  getFavouriteDoctors(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): FavouriteDoctorsConnection!
  getFeaturedPartner(data: GetFeaturedPartnerInput!): Partner!
  getHospitalMedications(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int): HospitalMedicationsConnection!
  getIllness(id: String!): IllnessOutput!
  getIllnesses(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): IllnessConnection!
  getIssue(issueId: Float!): IssueConnection!
  getIssues: IssuesConnection!
  getLGAsFromWellaHealth(data: WellaHealthPharmacyInput!): WellaLGAsConnection!
  getLabResult(id: String!): LabResultPayload!
  getLabResults(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): LabArrayPayload!
  getMedication(data: PatientMedicationInput!): MedicationsPayload!
  getMedications(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): MedicationsArrayPayload!
  getMessage(data: MessagingInput!): Message!
  getMessages(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): MessageConnection!
  getMicroInsurancePlan(filterBy: FilterPlansInput!): JSON!
  getMicroInsuranceRevenue(filterBy: FilterRegistrationsInput): JSON!
  getMicroInsuranceStats(filterBy: StatisticsInput!): JSON!
  getMyAppointments(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): AppointmentConnection!
  getMyConsultations(q: String!): ConsultationConnection!
  getMyEarnings(after: String, before: String, endDate: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, startDate: String): EarningConnection!
  getMyPatients: DoctorPatientConnection!
  getMyTransactions(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): TransactionReferenceConnection!
  getNemEnrollee(data: HmoIdInput!): JSON!
  getNemPlanBenefit(data: PlanCodeInput!): JSON!
  getNemPlanProviders(data: PlanCodeInput!): JSON!
  getNemPlansForHeala: JSON!
  getNewConsultation(data: ConsultationId!): Consultation!
  getNextConsultations(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): ConsultationConnection!
  getNotifications(filter: FilterInputType, user: String!): NotificationsConnection!
  getPartner(data: PartnerInput!): Partner!
  getPartnerBySubdomain(data: GetPartnerSubdomainInput!): PartnerConfiguration!
  getPartnerCategories(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): PartnerCategoriesConnection!
  getPartnerConfiguration(data: GetPartnerConfigurationInput!): PartnerConfiguration!
  getPartners(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): PartnersConnection!
  getPatientConsultationDetails(data: ConsultationId!): JSON!
  getPayoutDoctors(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, search: String): DoctorProfileArrayPayload!
  getPayouts(after: String, before: String, endDate: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, startDate: String): PayoutConnection!
  getPayoutsProviders(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): ProviderTypeConnection!
  getPermission(data: PermissionInput!): Permission!
  getPermissions(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): PermissionConnection!
  getPharmaciesFromWellaHealth(data: WellaHealthPharmacyInput!): WellaHealthPharmaciesConnection!
  getPharmacyDashboard(data: GetPharmacyDashboardInput!): PharmacyDashboard!
  getPharmacyDrugs(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int): PharmacyDrugsConnection!
  getPlan(id: String!): PlanPayload!
  getPlans(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, user: String): PlansPayload!
  getPrescription(data: AddPrescription!): Prescription!
  getPrescriptions(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): PrescriptionsConnection!
  getProvider(id: String!): Provider!
  getProviders(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, search: String): AllProviderConnection!
  getReferral(data: GetReferralInput!): Referral!
  getReferrals(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): ReferralConnection!
  getRejectedDrugs(data: IdInput!): RejectedPayload!
  getReminder(data: ReminderInput!): ReminderPayload!
  getReminders(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): ReminderArrayPayload!
  getRequests(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): RequestsConnection!
  getRole(id: ID!): Role!
  getRoles(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, search: String): Roles!
  getServerTime: ServerTime!
  getSignUpStats(filterBy: FilterRegistrationsInput): JSON!
  getSingleFamily(data: FamilyInput!): FamilyPayload!
  getSpecialization(data: IdInput!): Specialization!
  getSpecializations(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, search: String): SpecializationPayload!
  getStats(filterBy: JSON): AllStats!
  getSubscription(data: GetSubscriptionInput): JSON!
  getSymptoms: SymptomsConnection!
  getTransactions(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): TransactionReferenceConnection!
  getUserFcmToken(user: String!): FcmToken!
  getUserType(id: String!): UserType!
  getUserTypeProviders(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): ProviderTypeConnection!
  getUserTypes(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, search: String): UserTypeConnection!
  getVerification(data: GetVerificationInput!): Verification!
  getVerifications(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, search: String): VerificationsConnection!
  hello: String!
  me: SinglePopulatedAccount!
  nextStepsBusiness: JSON!
  nextStepsFamily: JSON!
  prescriptionReferral(data: PrescriptionReferralInput!): PopulatedPrescriptionReferral!
  prescriptionReferrals(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, search: String): PrescriptionReferralPayload!
  profile(id: String!): PopulatedProfile!
  profiles(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, search: String): ProfileConnection!
  profilesByPlan(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, search: String): ProfileConnection!
  profilesByStatus(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, search: String): ProfileConnection!
  quotes(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String): QuotesPayload!
  sendNewsLetter(data: NewsLetterInput!): Boolean!
  testApproval(data: IdInput!): PopulatedTestsApprovals!
  testApprovals(after: String, before: String, filterBy: JSON, first: Int, last: Int, orderBy: String, page: Int, q: String, search: String): TestsApprovalsPayload!
}

type Quote {
  comment: String
  companyName: String
  createdAt: DateTime
  email: String
  firstName: String
  lastName: String
  noOfEmployees: Float
  phoneNumber: String
  state: String
  status: String
  updatedAt: DateTime
}

input QuoteInput {
  quoteId: String!
}

input QuoteStatusInput {
  quoteId: String!
  status: String!
}

type QuotesPayload {
  data: [Quote!]
  pageInfo: PageInfo
}

type Rating {
  comment: String
  review: String
  score: Int
}

input RebroadCastNotificationInput {
  id: ID!
}

input Reference {
  reference_code: String!
}

type Referral {
  _id: ID!
  consultationId: String
  createdAt: DateTime
  doctor: DoctorProfile
  facility: String
  note: String
  patient: Profile
  providerId: String
  provisionalDiagnosis: String
  reason: String
  specialization: String
  status: String
  testType: String
  tests: [DiagnosticLabTest!]
  trackingId: String
  type: String
  updatedAt: DateTime
  urgency: String
}

type ReferralConnection {
  pageInfo: PageInfo
  referral: [Referral!]!
}

type ReferralDosageFrequency {
  duration: Float
  timing: Float
}

type ReferralPayload {
  errors: [ErrorPayload!]
  referral: Referral
}

type RefreshPayload {
  access_token: String!
  message: String
  refresh_token: String!
}

input RegisterBusinessInput {
  address: String
  email: String!
  firstName: String!
  lastName: String!
  name: String!
  noOfEmployees: Float!
  password: String!
  type: String!
}

type RegisterBusinessPayload {
  access_token: String
  account: Account
  business: Business
  profile: Profile
  refresh_token: String
}

input RegisterFamilyInput {
  dob: String!
  email: String!
  externalProvider: String!
  externalProviderId: String!
  firstName: String!
  gender: String!
  lastName: String!
  noOfDependant: Float!
  password: String!
  phoneNumber: String!
}

type RegisterFamilyPayload {
  access_token: String
  account: Account
  profile: Profile
  refresh_token: String
}

input RegisterMicroInsuranceIndividualInput {
  dob: String!
  email: String!
  externalProvider: String!
  externalProviderId: String!
  firstName: String!
  gender: String!
  lastName: String!
  password: String!
  phoneNumber: String!
}

type RegisterMicroInsuranceIndividualPayload {
  access_token: String
  account: Account
  profile: Profile
  refresh_token: String
}

input RejectVerificationInput {
  reason: String!
  verificationId: String!
}

type RejectedPayload {
  account: Account
  drugs: [Drug!]
}

type RejectionVerificationPayload {
  message: String!
  reason: String!
}

type Reminder {
  _id: ID
  createdAt: DateTime!
  date: DateTime!
  description: String!
  interval: String!
  patient: String!
  type: String!
  updatedAt: DateTime!
}

type ReminderArrayPayload {
  errors: [ErrorPayload!]
  message: String!
  pageInfo: PageInfo!
  reminder: [Reminder!]!
}

input ReminderInput {
  id: String!
}

type ReminderPayload {
  errors: [ErrorPayload!]
  message: String!
  reminder: Reminder!
}

input RemoveDependantInput {
  dependantId: String!
  employeeId: String!
}

input RenewLicenseInput {
  expiry_date: String!
  image: String!
  number: String!
  type: String!
}

type Request {
  _id: String!
  createdAt: DateTime!
  firstName: String!
  lastName: String!
  location: String!
  name: String!
  type: String!
  updatedAt: DateTime!
}

type RequestPayload {
  errors: [ErrorPayload!]
  message: String!
  request: Request!
}

input RequestReferralInput {
  consultationId: String
  doctor: String!
  facility: String
  note: String!
  patient: String!
  provisionalDiagnosis: String
  reason: String!
  specialization: String
  tests: [DiagnosticLabTestInput!]
  type: String!
  urgency: String
}

type RequestsConnection {
  data: [Request!]!
  pageInfo: PageInfo!
}

input RescheduleConsultationInput {
  doctor: ID!
  id: ID!
  rescheduleReason: String!
  time: String!
}

input ResendEmployeeInput {
  businessId: String!
  email: String!
}

input ResendInput {
  email: String!
}

input ResetInput {
  email: String!
}

input ResolveDisputeInput {
  disputeResolvedReason: String!
  disputeStatus: String!
  id: ID!
  isDisputed: Boolean!
  status: String
}

input RestoreMyAccountInput {
  email: String!
  password: String
}

type Role {
  _id: ID
  createdAt: DateTime
  description: String
  editable: Boolean
  name: String
  permissions: [String!]
  updatedAt: DateTime
}

type Roles {
  pageInfo: PageInfo
  role: [Role!]!
}

input SendNotificationsInput {
  content: String!
  dryRun: Boolean
  fcmToken: String
  fcmTokens: [String!]
  group: String
  itemId: ID!
  previewImageUri: String
  previewImageUriThumbnail: String
  role: String
  saveNotification: Boolean
  tag: String!
  ticker: String!
  title: String!
  useSound: Boolean
  users: [ID!]
}

type ServerTime {
  hourMin: String!
  hourMinLagos: String!
  time: String!
  timeJSdate: String!
  timeLagos: String!
  timeLagosJsDate: String!
}

input SetAvailabilityInput {
  available: Boolean!
  day: String!
  doctor: String!
  providerId: String!
  times: [AvailabilityArray!]!
}

input SetPermanentPassword {
  email: String!
  newPassword: String!
  temporaryPassword: String!
}

type Setting {
  _id: ID
  key: String
  name: String!
  value: String!
}

type SettingPayload {
  errors: [ErrorPayload!]!
  message: String
  setting: Setting
}

type SignupPayload {
  account: Account!
  message: String!
}

input SignupUserInput {
  authType: String!
  email: String!
  enrolleeNumber: String
  password: String!
  providerId: String
  referralCode: String
  role: String!
  type: String
  userTypeId: String
}

type SinglePopulatedAccount {
  _id: ID
  access_token: String
  apiKey: String
  authType: String
  createdAt: DateTime
  deactivateType: String
  deactivated: Boolean
  deactivatedAt: String
  dociId: String
  email: String
  enrolleeNumber: String
  isActive: Boolean
  isEmailVerified: Boolean
  isPasswordTemporary: Boolean
  nextStep: String
  otpTokenExpiresAt: String
  providerId: ProviderType
  refresh_token: String
  role: String
  updatedAt: DateTime
  userTypeId: UserAccountType
}

type SinglePrescription {
  _id: ID!
  consultation: String
  createdAt: DateTime
  doctor: String
  drugs: [PrescriptionDrug!]
  patient: String
  updatedAt: DateTime
}

type SingleReferral {
  _id: ID!
  consultationId: String
  createdAt: DateTime
  doctor: String
  note: String
  patient: String
  providerId: String
  provisionalDiagnosis: String
  reason: String
  specialization: String
  tests: [DiagnosticLabTest!]
  trackingId: String
  type: String
  updatedAt: DateTime
  urgency: String
}

input SocialSignupUserInput {
  authType: String!
  deviceId: String!
  email: String!
  enrolleeNumber: String
  isMicroInsurance: Boolean
  isMicroInsuranceBusiness: Boolean
  isMicroInsuranceBusinessPrincipal: Boolean
  isMicroInsuranceFamily: Boolean
  isMicroInsuranceFamilyPrincipal: Boolean
  isMicroInsuranceUser: Boolean
  providerId: String
  referralCode: String
  role: String!
  userTypeId: String
}

type SpecialisationArray {
  Accuracy: String
  ID: String
  Name: String
  Ranking: String
  SpecialistID: String
}

type Specialist {
  _id: String
  approved: String
  facility: String
  note: String
  reason: String
  specialization: String
  trackingId: String
  urgency: String
}

input SpecialistInput {
  facility: String
  note: String
  reason: String
  specialization: String
  trackingId: String
  urgency: String
}

type Specialization {
  _id: String
  createdAt: DateTime
  name: String
  updatedAt: DateTime
}

type SpecializationPayload {
  data: [Specialization!]
  pageInfo: PageInfo
}

type Stat {
  acceptedChartData: [JSON!]
  activeChartData: [JSON!]
  cancelledChartData: [JSON!]
  chartData: [JSON!]
  completedChartData: [JSON!]
  declinedChartData: [JSON!]
  diagnosticsChartData: [JSON!]
  hospitalChartData: [JSON!]
  inactiveChartData: [JSON!]
  ongoingChartData: [JSON!]
  pendingChartData: [JSON!]
  pharmacyChartData: [JSON!]
  total: Float
  totalAccepted: Float
  totalActive: Float
  totalCancelled: Float
  totalCompleted: Float
  totalDeclined: Float
  totalDiagnostics: Float
  totalHospitals: Float
  totalInactive: Float
  totalOngoing: Float
  totalPending: Float
  totalPharmacies: Float
}

input StatisticsInput {
  month: Float!
  year: Float
}

type Stats {
  appointmentStats: AppointmentStats
  availabilityCalendar: AvailableTimeStats
  doctorStats: DaysStats
  patientStats: DaysStats
  subscribers: DaysStats
  totalEarnings: Float
  totalPayout: Float
}

type SubscriptionType {
  _id: String
  amount: Float
  emailToken: String
  planId: PlanType
  providerId: ID
  status: String
  subscriptionCode: String
  subscriptionExpiresAt: DateTime
  subscriptionPurchasedAt: DateTime
  usages: Float
  userId: String
}

type Symp {
  name: String
}

input Symptom {
  name: String!
}

type Symptoms {
  Description: String
  DescriptionShort: String
  ID: String
  MedicalCondition: String
  Name: String
  PossibleSymptoms: String
  ProfName: String
  Synonyms: String
  TreatmentDescription: String
  _id: String
  createdAt: DateTimeScalar
  updatedAt: DateTimeScalar
}

type SymptomsConnection {
  symptoms: [Symptoms!]!
}

type SymptomsDiagnosis {
  Issue: IssueObj!
  Specialisation: [SpecialisationArray!]!
}

type Test {
  _id: String
  approved: String
  name: String
  notes: String
  price: Float
  tat: String
  urgency: String
}

input TestInput {
  name: String!
  notes: String
  price: Float!
  tat: String
  urgency: String!
}

type TestResults {
  file: String
  title: String
}

input TestResultsInput {
  file: String!
  title: String!
}

type TestsApprovals {
  _id: String
  consultation: String
  createdAt: DateTime
  doctor: String
  note: String
  partner: String
  patient: String
  providerId: String
  referral: String
  status: String
  tests: [Test!]
  updatedAt: DateTime
}

type TestsApprovalsPayload {
  data: [PopulatedTestsApprovals!]
  pageInfo: PageInfo
}

type Ticket {
  _id: ID!
  createdAt: String
  createdBy: String
  description: String
  priority: String
  status: String
  title: String!
  type: String
  updatedAt: String
}

type TicketPayload {
  message: String
  ticket: Ticket
}

type TransactionReference {
  _id: String!
  amount: String!
  brand: String!
  createdAt: DateTimeScalar!
  failedPermanently: String!
  itemId: String
  last4: String!
  paymentGateway: String!
  reason: String!
  reference: String!
  retries: String!
  saveCard: Boolean!
  status: String
  updatedAt: DateTimeScalar!
  used: Boolean!
  user: String!
}

type TransactionReferenceConnection {
  data: [TransactionReference!]!
  pageInfo: PageInfo!
}

type UnPopulatedConsultation {
  _id: ID!
  appointmentAcceptedAt: String
  appointmentStartedAt: String
  companyId: String
  consultationDuration: String
  consultationOwner: String
  contactMedium: String
  createdAt: DateTime
  createdThrough: String
  declineReason: String
  description: String
  diagnosis: [Diag!]
  discomfortLevel: String
  disputeReason: String
  disputeResolvedReason: String
  disputeStatus: String
  doctor: String
  doctorEndCommunicationReason: String
  doctorJoined: Boolean
  doctorNote: String
  doctorSatisfactionReason: String
  doctorSatisfied: Boolean
  externalPharmacy: String
  externalProvider: String
  fee: Int
  firstNotice: String
  followUpConsultationId: JSON
  isDisputeResolved: Boolean
  isDisputed: Boolean
  isFollowUp: Boolean
  joined: Boolean
  notificationSchedules: [NotificationSchedule!]
  paid: Boolean
  path: String
  patient: String
  patientEndCommunicationReason: String
  patientJoined: Boolean
  patientSatisfactionReason: String
  patientSatisfied: Boolean
  pharmacyAddress: String
  pharmacyCode: String
  pharmacyName: String
  prescription: [Prescription!]
  principalHmoId: String
  providerId: String
  rating: Rating
  reason: String
  referralId: String
  status: String
  symptoms: [Symp!]
  time: DateTime
  trackingId: String
  type: String
  updatedAt: DateTime
  wasDisputed: Boolean
}

type UnPopulatedDoctorProfile {
  _id: ID
  accountDetails: String
  accountId: String
  address: String
  balance: Int
  cadre: String
  consultationReminderNotification: Boolean
  consultationRequestNotification: Boolean
  createdAt: DateTime
  dob: DateTime
  dociId: String
  email: String
  fee: Float
  firstName: String
  gender: String
  hospital: String
  image: String
  instantConsultationNotification: Boolean
  lastName: String
  phoneNumber: String
  picture: String
  providerId: String
  rating: Int
  specialization: String
  status: String
  updatedAt: DateTime
}

type UnPopulatedEmployee {
  businessId: String
  createdAt: DateTime
  dob: String
  email: String
  name: String
  noofdependants: Float
  relationship: String
  status: String
  updatedAt: DateTime
}

type UnPopulatedEmployeePayload {
  employees: [UnPopulatedEmployee!]
}

"""UnsignedInt custom scalar type"""
scalar UnsignedInt

input UpdateAllergyInput {
  food: String
  id: String!
  medication: String
  severity: String
}

input UpdateAppointmentInput {
  date: String
  description: String
  doctor: String
  id: String!
  patient: String
  providerId: String
  time: String
}

input UpdateAvailableTimeInput {
  day: String
  id: String!
  providerId: String
  times: [AvailabilityArray!]
}

input UpdateBusinessInput {
  address: String
  id: Int!
  name: String
  noOfEmployees: Float
  type: String
}

input UpdateCompanyInput {
  address: String
  email: String
  id: String!
  logo: String
  name: String
  phone: String
  providerId: String
}

input UpdateConsultationInput {
  consultationId: String
  consultationOwner: String
  contactMedium: String
  description: String
  diagnosis: [Diagnosis!]
  discomfortLevel: String
  doctor: String
  doctorJoined: Boolean
  doctorNote: String
  firstNotice: String
  id: ID!
  joined: Boolean
  messages: [ChatMessageInput!]
  patient: String
  patientJoined: Boolean
  pharmacyAddress: String
  pharmacyName: String
  providerId: String
  reason: String
  referralId: String
  scheduleIds: [String!]
  serverTime: String
  status: String
  symptoms: [Symptom!]
  time: String
  type: String
}

input UpdateDiagnosticLabTestInput {
  id: String!
  name: String
  partner: String
  price: Float
  tat: String
}

input UpdateDoctorPatient {
  doctor: String!
  id: String!
  patient: String!
}

input UpdateDoctorProfileInput {
  address: String
  cadre: String
  consultationReminderNotification: Boolean
  consultationRequestNotification: Boolean
  dob: DateTime
  fee: Float
  firstName: String
  gender: String
  hospital: String
  id: String!
  instantConsultationNotification: Boolean
  lastName: String
  phoneNumber: String
  picture: String
  specialization: String
}

input UpdateDoctorProviderInput {
  dociId: String!
  providerId: String
}

type UpdateDoctorProviderPayload {
  account: Account
  profile: DoctorProfile
}

input UpdateDrugInput {
  amount: Float
  approved: String
  dosageFrequency: DosageFrequencyInput
  dosageQuantity: Float
  dosageUnit: String
  drugForm: String
  drugName: String
  drugPrice: Float
  id: String!
  instructions: String
  markedUpPrice: Float
  markup: Float
  notes: String
  priceListId: Float
  quantity: Float
  referralId: String!
  route: String
  unitPrice: Float
}

input UpdateDrugOrderInput {
  deliveryOption: String
  id: String!
  note: String
  status: String
  userLocation: UserLocationInput
}

input UpdateEmailInput {
  email: String!
  password: String
}

input UpdateEmployeeInput {
  dob: String
  email: String
  employeeId: String!
  name: String
}

input UpdateEnrolleeInput {
  client: String
  companyId: String
  email: String
  expiryDate: DateTime
  firstName: String
  hmoId: String
  id: String!
  lastName: String
  phone: String
  photo: String
  plan: String
  planId: String
  providerId: String
  status: Boolean
}

input UpdateFamilyInput {
  admin: String
  dob: String
  email: String
  firstName: String
  gender: String
  id: String!
  image: String
  lastName: String
  phoneNumber: String
  profileId: String
  relationship: String
  status: String
}

input UpdateFcmTokenInput {
  deviceId: String!
  role: String!
  token: String!
  user: String!
}

input UpdateHospitalMedicationInput {
  deliveryOption: String
  id: String!
  note: String
  status: String
  userLocation: UserLocationInput
}

input UpdateIllnessInput {
  description: String!
  id: ID!
  name: String!
}

input UpdateInsurancePlanInput {
  benefits: [BenefitInput!]
  id: String!
  image: String
}

input UpdateJoinedInput {
  doctorJoined: Boolean
  id: String!
  patientJoined: Boolean
}

input UpdateLabInput {
  doctor: String
  id: String!
  partner: String
  patient: String
  url: String
}

input UpdatePartnerInput {
  address: String
  bankDetails: BankDetailsInput
  category: String
  classification: String
  email: String
  id: String!
  logoImageUrl: String
  name: String
  phone: String
  providerId: String
  specialisation: String
}

input UpdatePasswordInput {
  newPassword: String!
  password: String!
}

input UpdatePatientIllnessInput {
  pastIllness: [AddPastIllnessInput!]!
}

input UpdatePatientMedicationInput {
  dosage: Float
  id: String!
  interval: String
  name: String
}

input UpdatePayStackPlanInput {
  amount: Float
  currency: String
  description: String
  id: String!
  interval: String
  invoice_limit: Int
  name: String
  send_invoices: Boolean
  send_sms: Boolean
}

input UpdatePermissionInput {
  description: String
  id: String!
  name: String
}

input UpdatePharmacyDrugInput {
  drugName: String!
  drugPrice: Float!
  id: String!
  notes: String!
  partner: String!
  priceListId: Float!
  quantity: Float!
  unitPrice: Float!
}

type UpdatePharmacyDrugPayload {
  errors: [ErrorPayload!]!
  pharmacyDrug: PharmacyDrug!
}

input UpdatePlanInput {
  allowedFeatures: JSON
  amount: Float
  description: String
  duration: String
  id: String!
  name: String
  noOfDoctors: Float
  provider: ID
  specialisation: String
  type: String
}

input UpdatePrescriptionReferralInput {
  drugs: [String!]
  drugsPartner: String
  referralId: String!
  specialist: SpecialistInput
  status: String!
  tests: [String!]
  testsPartner: String
}

input UpdatePriority {
  end: Float
  id: String!
  start: Float
}

input UpdateProfileInput {
  address: String
  bloodGroup: String
  dob: String
  externalPlanCode: String
  externalPlanType: String
  externalProvider: String
  externalProviderId: String
  firstName: String
  gender: String
  genotype: String
  height: Int
  hmoId: String
  image: String
  lastName: String
  phoneNumber: String
  timezoneOffset: String
  weight: Int
}

input UpdateProviderInput {
  address: String
  email: String
  hmoPlans: [HmoPlansInput!]
  icon: String
  iconAlt: String
  id: String!
  monthlyConsultationLimit: Int
  name: String
  phone: String
  planId: String
  rareCase: Boolean
  userTypeId: String
}

input UpdateReferralInput {
  id: String!
  status: String!
}

input UpdateRegenerateInput {
  address: String
  email: String
  hmoPlans: [HmoPlansInput!]
  icon: String!
  iconAlt: String!
  id: String!
  name: String!
  phone: String
  planId: String
  rareCase: Boolean
  userTypeId: String!
}

input UpdateReminderInput {
  date: DateTime
  description: String
  id: String!
  interval: String
  type: String
}

input UpdateRoleInput {
  description: String
  id: String!
  name: String
  permissions: [String!]
  type: String
}

input UpdateSatisFactionInput {
  doctorSatisfactionReason: String
  doctorSatisfied: Boolean
  id: String!
  patientSatisfactionReason: String
  patientSatisfied: Boolean
}

input UpdateSettingInput {
  id: String!
  key: String
  name: String
  value: String
}

input UpdateSpecializationInput {
  id: String!
  name: String
}

input UpdateTestInput {
  approved: String
  id: String!
  name: String
  notes: String
  price: Float
  referralId: String!
  tat: String
  urgency: String
}

input UpdateUserProviderInput {
  dociId: String!
  providerId: ID!
}

input UpdateUserTypeInput {
  description: String
  icon: String
  id: String!
  name: String
}

input UploadChatMessageInput {
  consultationId: ID!
  content: String!
  receiver: ID!
  receiverRole: String!
  sender: ID!
  senderRole: String!
}

input UploadEmployeesInput {
  bucket: String!
  businessId: String!
  fileUrl: String!
}

type UploadEmployeesPayload {
  result: UploadEmployeesResult
}

type UploadEmployeesResult {
  bucket: String!
  fileUrl: String!
  totalInserted: Float!
}

input UploadEnrolleesInput {
  bucket: String!
  companyId: String
  fileUrl: String!
  override: Boolean
  planId: String!
  providerId: String!
  replace: Boolean
}

type UploadEnrolleesPayload {
  errors: [ErrorPayload!]!
  result: UploadEnrolleesResult!
}

type UploadEnrolleesResult {
  bucket: String!
  fileUrl: String!
  totalInserted: Float!
}

input UploadLabTestsInput {
  bucket: String!
  fileUrl: String!
}

type UploadLabTestsPayload {
  result: UploadLabTestsResult
}

type UploadLabTestsResult {
  bucket: String
  fileUrl: String
  totalInserted: Float
}

input UploadPharmacyDrugsInput {
  fileUrl: String!
  partner: String!
}

type UploadPharmacyDrugsPayload {
  data: [PharmacyDrug!]!
  errors: [ErrorPayload!]!
}

type UserAccountType {
  _id: ID!
  description: String
  icon: String
  name: String
}

input UserLocationInput {
  address: String!
  city: String!
  landmark: String!
  lat: Float!
  lga: String!
  lng: Float!
  phoneNumber: String!
  state: String!
}

input UserPlanInput {
  user: String!
}

type UserType {
  _id: ID
  createdAt: DateTime
  description: String
  icon: String
  name: String
  providerCount: Int
  updatedAt: DateTime
}

type UserTypeConnection {
  pageInfo: PageInfo!
  userType: [UserType!]!
}

type UserTypePayload {
  errors: [ErrorPayload!]!
  message: String
  userType: UserType
}

input ValidateEnrolleeInput {
  hmoId: String!
  providerId: String!
}

type Verification {
  _id: ID
  alumni_association: JSON
  createdAt: DateTime
  external_reference: JSON
  license: JSON
  licenseRenewalStatus: String
  profileId: JSON
  qualification: JSON
  reason: String
  reference: JSON
  status: Boolean
  updatedAt: DateTime
  yearbook: JSON
}

type VerificationPayload {
  data: Verification!
  errors: [ErrorPayload!]!
  message: String!
}

type VerificationsConnection {
  pageInfo: PageInfo!
  verification: [Verification!]!
}

input VerifyEmailInput {
  email: String!
  otp: String!
}

type VerifyEmailPayload {
  account: Account!
  message: String!
}

input VerifyHcpInput {
  id: String!
}

type WellaHealthPharmaciesConnection {
  data: [WellaHealthPharmacy!]!
}

type WellaHealthPharmacy {
  address: String
  area: String
  lga: String
  pharmacyCode: String
  pharmacyName: String
  state: String
}

input WellaHealthPharmacyInput {
  lga: String
  state: String!
}

type WellaLGAsConnection {
  data: [String!]!
}

input Yearbook {
  graduation_year: String!
  image: String!
}