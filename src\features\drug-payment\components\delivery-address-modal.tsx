import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { AddressLabel } from "./address-label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { statesAndLgas } from "@/lib/states-and-lgas";
import { DeliveryAddress } from "../types";
import { MapPin, AlertCircle, Loader2 } from "lucide-react";

interface DeliveryAddressModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (address: DeliveryAddress) => void;
}

export function DeliveryAddressModal({
  isOpen,
  onClose,
  onSave,
}: DeliveryAddressModalProps) {
  const [error, setError] = useState<string | null>(null);
  const [address, setAddress] = useState<DeliveryAddress>({
    street: "",
    city: "",
    landmark: "",
    state: "",
    lga: "",
    latitude: 0,
    longitude: 0,
  });

  // Geolocation states
  const [locationLoading, setLocationLoading] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [hasLocation, setHasLocation] = useState(false);

  // Request user's current location
  const requestLocation = async () => {
    if (!navigator.geolocation) {
      setLocationError("Geolocation is not supported by this browser.");
      return;
    }

    setLocationLoading(true);
    setLocationError(null);

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        setAddress((prev) => ({
          ...prev,
          latitude,
          longitude,
        }));
        setHasLocation(true);
        setLocationLoading(false);
        setLocationError(null);
      },
      (error) => {
        setLocationLoading(false);
        let errorMessage = "Unable to retrieve your location.";

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = "Location access was denied. Please enable location permissions and try again.";
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = "Location information is unavailable.";
            break;
          case error.TIMEOUT:
            errorMessage = "Location request timed out.";
            break;
        }

        setLocationError(errorMessage);
        setHasLocation(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000, // 5 minutes
      }
    );
  };

  // Request location when modal opens and clear errors
  useEffect(() => {
    if (isOpen) {
      setError(null); // Clear any previous errors when modal opens
      if (!hasLocation && !locationLoading) {
        requestLocation();
      }
    }
  }, [isOpen, hasLocation, locationLoading]);

  // Clear errors and reset form when modal closes
  const handleClose = () => {
    setError(null);
    setLocationError(null);
    setAddress({
      street: "",
      city: "",
      landmark: "",
      state: "",
      lga: "",
      latitude: 0,
      longitude: 0,
    });
    setHasLocation(false);
    onClose();
  };

  const handleUpdateAddress = (name: string, value: string) => {
    setError(null);

    if (!name || name === "") return;
    if (name === "state") {
      setAddress((prev) => ({
        ...prev,
        lga: "",
        [name]: value,
      }));
    } else {
      setAddress((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleSave = () => {
    // Check if location is obtained
    if (!hasLocation) {
      setError("Please allow location access to continue.");
      return;
    }

    // Check if required fields are filled
    const missingFields = [];
    if (!address.state) missingFields.push("State");
    if (!address.lga) missingFields.push("LGA");
    if (!address.city) missingFields.push("City");
    if (!address.street) missingFields.push("Street");
    if (!address.landmark) missingFields.push("Landmark");

    if (missingFields.length > 0) {
      setError(`Please fill in the following required fields: ${missingFields.join(", ")}`);
      return;
    }

    onSave(address);
    handleClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>Enter your delivery address</DialogTitle>
          <DialogDescription>
            This address will be used to deliver your drugs.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4 px-1 overflow-y-auto flex-1 min-h-0">
          {/* Location Notice */}
          {locationError && (
            <div className="flex items-start gap-3 p-4 border border-orange-200 bg-orange-50 rounded-md">
              <AlertCircle className="h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0" />
              <div className="text-orange-800 space-y-2">
                <p className="font-medium">Location Access Required</p>
                <p className="text-sm">{locationError}</p>
                <p className="text-sm">
                  We need your location to ensure accurate delivery. Please enable location access to continue.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={requestLocation}
                  disabled={locationLoading}
                  className="mt-2"
                >
                  {locationLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Getting Location...
                    </>
                  ) : (
                    <>
                      <MapPin className="mr-2 h-4 w-4" />
                      Retry Location
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}

          {locationLoading && !locationError && (
            <div className="flex items-start gap-3 p-4 border border-blue-200 bg-blue-50 rounded-md">
              <Loader2 className="h-4 w-4 animate-spin text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-blue-800">
                <p className="font-medium">Getting your location...</p>
                <p className="text-sm">Please allow location access when prompted.</p>
              </div>
            </div>
          )}

          {hasLocation && (
            <div className="flex items-start gap-3 p-4 border border-green-200 bg-green-50 rounded-md">
              <MapPin className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
              <div className="text-green-800">
                <p className="font-medium">Location obtained successfully!</p>
                <p className="text-sm">Your delivery location has been set.</p>
              </div>
            </div>
          )}

          <div>
            <AddressLabel htmlFor="street" className="mb-1 block" required>Street</AddressLabel>
            <Input
              id="street"
              value={address.street}
              onChange={(e) => handleUpdateAddress("street", e.target.value)}
              placeholder="Enter your street address"
            />
          </div>

          <div>
            <AddressLabel htmlFor="city" className="mb-1 block" required>City</AddressLabel>
            <Input
              id="city"
              value={address.city}
              onChange={(e) => handleUpdateAddress("city", e.target.value)}
              placeholder="Enter your city"
            />
          </div>

          <div>
            <AddressLabel htmlFor="state" className="mb-1 block" required>State</AddressLabel>
            <Select
              value={address.state}
              onValueChange={(value) => handleUpdateAddress("state", value)}
            >
              <SelectTrigger id="state" className="w-full">
                <SelectValue placeholder="Select a state" />
              </SelectTrigger>
              <SelectContent className="max-h-60">
                {Object.keys(statesAndLgas).map((state) => (
                  <SelectItem key={state} value={state}>
                    {state}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <AddressLabel htmlFor="lga" className="mb-1 block" required>LGA</AddressLabel>
            <Select
              value={address.lga}
              onValueChange={(value) => handleUpdateAddress("lga", value)}
              disabled={!address.state}
            >
              <SelectTrigger id="lga" className="w-full">
                <SelectValue placeholder={address.state ? "Select LGA" : "Select a state first"} />
              </SelectTrigger>
              <SelectContent className="max-h-60">
                {(statesAndLgas[address.state] || []).map((lga) => (
                  <SelectItem key={lga} value={lga}>
                    {lga}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <AddressLabel htmlFor="landmark" className="mb-1 block" required>Closest Landmark</AddressLabel>
            <Input
              id="landmark"
              value={address.landmark || ""}
              onChange={(e) => handleUpdateAddress("landmark", e.target.value)}
              placeholder="Enter the closest landmark (optional)"
            />
          </div>

        </div>

        {/* Fixed footer with buttons */}
        <div className="flex-shrink-0 pt-4 border-t border-gray-200">
          {/* Error message display in footer for better visibility */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-red-700 font-medium">
                  {error}
                </p>
              </div>
            </div>
          )}

          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={!hasLocation || locationLoading}
            >
              {locationLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Getting Location...
                </>
              ) : (
                "Save Address"
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
