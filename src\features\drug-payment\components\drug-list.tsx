import { useState } from "react";
import { DrugItem } from "../types";
import { useDrugPaymentStore } from "../hooks/use-drug-payment-store";
import { RemoveDrugConfirmationModal } from "./remove-drug-confirmation-modal";

interface DrugListProps {
  drugs: DrugItem[];
}

export function DrugList({ drugs }: DrugListProps) {
  const { removeItem } = useDrugPaymentStore();
  const [isConfirmationOpen, setIsConfirmationOpen] = useState(false);
  const [drugToRemove, setDrugToRemove] = useState<DrugItem | null>(null);

  const handleRemoveClick = (drug: DrugItem) => {
    setDrugToRemove(drug);
    setIsConfirmationOpen(true);
  };

  const handleConfirmRemove = () => {
    if (drugToRemove) {
      removeItem(drugToRemove._id);
    }
    setIsConfirmationOpen(false);
    setDrugToRemove(null);
  };

  const handleCancelRemove = () => {
    setIsConfirmationOpen(false);
    setDrugToRemove(null);
  };

  if (drugs.length === 0) {
    return (
      <div className="py-4 text-center text-gray-500">
        No drugs in this order
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        {drugs.map((drug) => (
          <div key={drug._id} className="flex items-start">
            <div className="h-3 w-3 mt-1.5 rounded-full bg-emerald-500 flex-shrink-0" />
            <div className="ml-3 flex-grow">
              {/* Desktop layout: side-by-side */}
              <div className="hidden sm:flex justify-between items-start gap-2">
                <div>
                  <h3 className="font-medium text-gray-900">
                    {drug.drugName} {drug.drugForm ? `(${drug.drugForm})` : ''}
                  </h3>
                  <p className="text-sm text-gray-500 mt-1">Quantity: {drug.quantity}</p>
                </div>
                <div className="flex flex-col items-end">
                  <span className="text-primary font-medium">
                    ₦{((drug.markedUpDrugPrice || 0) * drug.quantity).toLocaleString('en-NG', { maximumFractionDigits: 2 })}
                  </span>
                  <button
                    onClick={() => handleRemoveClick(drug)}
                    className="text-sm text-red-500 mt-1 hover:text-red-700"
                  >
                    Remove
                  </button>
                </div>
              </div>

              {/* Mobile layout: stacked vertically */}
              <div className="sm:hidden">
                <div>
                  <h3 className="font-medium text-gray-900">
                    {drug.drugName} {drug.drugForm ? `(${drug.drugForm})` : ''}
                  </h3>
                  <p className="text-sm text-gray-500 mt-1">Quantity: {drug.quantity}</p>
                </div>
                <div className="flex justify-between items-center mt-3">
                  <span className="text-primary font-medium">
                    ₦{((drug.markedUpDrugPrice || 0) * drug.quantity).toLocaleString('en-NG', { maximumFractionDigits: 2 })}
                  </span>
                  <button
                    onClick={() => handleRemoveClick(drug)}
                    className="text-sm text-red-500 hover:text-red-700"
                  >
                    Remove
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <RemoveDrugConfirmationModal
        isOpen={isConfirmationOpen}
        onClose={handleCancelRemove}
        onConfirm={handleConfirmRemove}
        drug={drugToRemove}
      />
    </>
  );
}
