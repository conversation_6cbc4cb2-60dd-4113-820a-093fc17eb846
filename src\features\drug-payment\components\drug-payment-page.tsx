import { useEffect, useState } from "react";
import { defaultDeliveryOptions, useDrugPaymentStore } from "../hooks/use-drug-payment-store";
import { DrugList } from "./drug-list";
import { DeliveryOptions } from "./delivery-options";
import { PaymentSummary } from "./payment-summary";
import { Button } from "@/components/ui/button";
import { HealaLoader } from "@/components/heala-loader";
import { X, Loader2 } from "lucide-react";
import { useNavigate } from "@tanstack/react-router";
import { useQuery as useApolloQuery, useLazyQuery, useMutation } from "@apollo/client";
import { getRejectedDrugsQuery, prescriptionReferralQuery, getPartnerConfigurationQuery, enrolleeDrugOrderMutation, initPaymentMutation } from "@/graphql/queries";
import { DrugPaymentData } from "../types";
import { useToast } from "@/hooks/use-toast";
import { Heala<PERSON>ogoWithText } from "@/components/icons/heala-logo-with-text";

interface DrugPaymentPageProps {
  referralId: string;
}

export function DrugPaymentPage({ referralId }: DrugPaymentPageProps) {
  const {
    paymentData,
    isLoading: storeLoading,
    error: storeError,
    selectedDeliveryOption,
    deliveryAddress,
    setPaymentData,
    setLoading,
    setError,
    updateDeliveryFee
  } = useDrugPaymentStore();

  // State to track the sequential loading process
  const [sequentialLoading, setSequentialLoading] = useState({
    rejectedDrugs: true,
    prescriptionReferral: true,
    partnerConfiguration: true
  });

  // State for order processing
  const [isProcessingOrder, setIsProcessingOrder] = useState(false);

  const navigate = useNavigate();
  const { toast } = useToast();

  // Mutations for order creation and payment
  const [createDrugOrder] = useMutation(enrolleeDrugOrderMutation);
  const [initializePayment] = useMutation(initPaymentMutation);

  // Fetch rejected drugs data
  const { loading: queryLoading, error: queryError } = useApolloQuery(getRejectedDrugsQuery, {
    variables: { referralId },
    fetchPolicy: 'network-only',
    onCompleted: (data) => {
      setSequentialLoading(prev => ({ ...prev, rejectedDrugs: false }));

      if (!data.getRejectedDrugs.drugs) {
        setError("No drug data found");
        return;
      }

      // Step 1: Extract and store access token
      const accessToken = data.getRejectedDrugs.account?.access_token;
      if (accessToken) {
        sessionStorage.setItem('token', accessToken);
      } else {
        console.warn("No access token found in rejected drugs response");
      }

      // Transform the data to match our DrugPaymentData structure
      const transformedData: DrugPaymentData = {
        id: referralId,
        patientName: "Patient", // This would come from the API in a real implementation
        patientEmail: "<EMAIL>", // This would come from the API in a real implementation
        drugs: data.getRejectedDrugs.drugs.map(drug => ({
          _id: drug._id || "",
          drugName: drug.drugName || "",
          drugForm: drug.drugForm || "",
          quantity: drug.quantity || 0,
          drugPrice: drug.amount || 0
        })),
        deliveryOptions: defaultDeliveryOptions,
      };

      setPaymentData(() => transformedData);

      // Step 2: Execute prescription referral query
      setSequentialLoading(prev => ({ ...prev, prescriptionReferral: true }));
      fetchPrescriptionReferral({
        variables: { id: referralId }
      });
    },
    onError: (error) => {
      setSequentialLoading(prev => ({ ...prev, rejectedDrugs: false }));
      console.error("Error fetching rejected drugs:", error);
      setError(error.message || "Failed to fetch payment data. Please try again.");
    }
  });

  // Prescription referral data fetching definition
  const [fetchPrescriptionReferral, { data: prescriptionReferralQueryResponse }] = useLazyQuery(prescriptionReferralQuery, {
    fetchPolicy: 'network-only',
    onCompleted: (data) => {
      setSequentialLoading(prev => ({ ...prev, prescriptionReferral: false }));

      if (data.prescriptionReferral?.drugsPartner?._id) {
        // Execute the third query to get partner configuration
        setSequentialLoading(prev => ({ ...prev, partnerConfiguration: true }));
        fetchPartnerConfiguration({
          variables: { partnerId: data.prescriptionReferral.drugsPartner._id }
        });
      } else {
        console.error("No drugs partner ID found in prescription referral data");
      }
    },
    onError: (error) => {
      setSequentialLoading(prev => ({ ...prev, prescriptionReferral: false }));
      console.error("Error fetching prescription referral:", error);
    }
  });
  const prescriptionReferralData = prescriptionReferralQueryResponse?.prescriptionReferral;

  // Partner configuration data fetching definition
  const [fetchPartnerConfiguration] = useLazyQuery(getPartnerConfigurationQuery, {
    fetchPolicy: 'network-only',
    onCompleted: (data) => {
      setSequentialLoading(prev => ({ ...prev, partnerConfiguration: false }));

      const deliveryFee = data.getPartnerConfiguration?.drugDeliveryFee;
      if (deliveryFee !== null && deliveryFee !== undefined) {
        updateDeliveryFee(deliveryFee);
      } else {
        const partnerId = data.getPartnerConfiguration?.partner || "not found";
        console.error(`Error: No delivery fee set for partner with id ${partnerId}`);
      }

      // Update drug prices based on partner markup
      const partnerDrugPriceMarkUp = data.getPartnerConfiguration?.drugPriceMarkUp
      if (partnerDrugPriceMarkUp) {
        setPaymentData(prev => {
          if (!prev || !prev.drugs) {
            console.error("No payment/drug data available to update drug prices");
            setError("Failed to compute drug price. Please try again.");
            toast({
              title: "Error",
              description: "Failed to compute drug price. Please try again.",
              variant: "destructive",
            });
            return prev;
          }

          const updatedDrugs = prev.drugs.map(drug => ({
            ...drug,
            markedUpDrugPrice: (drug.drugPrice * (partnerDrugPriceMarkUp / 100)) + drug.drugPrice
          }));

          return ({
            ...prev,
            drugs: updatedDrugs
          })
        });
      } else {
        const partnerId = data.getPartnerConfiguration.drugPriceMarkUp || "Unknown";
        console.error(`Error: No drug markup price set for partner with id ${partnerId}`);
      }
    },
    onError: (error) => {
      setSequentialLoading(prev => ({ ...prev, partnerConfiguration: false }));
      console.error("Error fetching partner configuration:", error);
    }
  });

  // Update loading state from query and sequential loading
  useEffect(() => {
    const isSequentialLoading = sequentialLoading.rejectedDrugs ||
      sequentialLoading.prescriptionReferral ||
      sequentialLoading.partnerConfiguration;
    setLoading(queryLoading || isSequentialLoading);
  }, [queryLoading, sequentialLoading, setLoading]);

  // Set initial loading state for the first query
  useEffect(() => {
    if (queryLoading) {
      setSequentialLoading(prev => ({ ...prev, rejectedDrugs: true }));
    }
  }, [queryLoading]);

  // Combine errors from store and query
  const error = storeError || (queryError ? queryError.message : null);
  const isLoading = storeLoading || queryLoading ||
    sequentialLoading.prescriptionReferral ||
    sequentialLoading.partnerConfiguration

  const handlePlaceOrder = async () => {
    // Check if delivery is selected but no address is provided
    if (selectedDeliveryOption === "delivery" && !deliveryAddress) {
      toast({
        title: "Delivery Address Required",
        description: "Please add a delivery address before proceeding.",
        variant: "destructive",
      });
      return;
    }

    // Check if we have all required data
    if (!prescriptionReferralData || !paymentData) {
      toast({
        title: "Missing Data",
        description: "Required data is not available. Please refresh and try again.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessingOrder(true);

    try {
      // Step 1: Create drug order
      console.log("Creating drug order...");

      // Calculate total amount
      const subtotal = paymentData.drugs.reduce((sum, drug) => sum + ((drug.markedUpDrugPrice || 0) * drug.quantity), 0);
      const selectedOption = paymentData.deliveryOptions.find(option => option.id === selectedDeliveryOption);
      const deliveryFee = selectedOption?.price || 0;
      const totalAmount = subtotal + deliveryFee;

      // Map delivery option
      const deliveryOption = selectedDeliveryOption === "delivery" ? "home" : "pickup";

      // Check for required fields in prescriptionReferralData and show toast if missing
      const missingFields = [];
      if (!prescriptionReferralData?.patient?._id) missingFields.push("patient._id");
      if (!prescriptionReferralData?.doctor?._id) missingFields.push("doctor._id");
      if (!prescriptionReferralData?.consultation?._id) missingFields.push("consultation._id");
      if (!prescriptionReferralData?.patient?.email) missingFields.push("patient.email");
      if (!prescriptionReferralData?.patient?.phoneNumber) missingFields.push("patient.phoneNumber");

      if (missingFields.length > 0) {
        toast({
          title: "Missing Data",
          description: `Required fields/data are missing: ${missingFields.join(", ")}`,
          variant: "destructive",
        });
        setIsProcessingOrder(false);
        return;
      }

      // Prepare drug order data
      const drugOrderData = {
        patient: prescriptionReferralData?.patient?._id,
        doctor: prescriptionReferralData?.doctor?._id,
        consultationId: prescriptionReferralData?.consultation?._id,
        drugIds: paymentData.drugs.map(drug => drug._id),
        deliveryOption,
        pharmacyCode: prescriptionReferralData?.consultation?.pharmacyCode,
        pharmacyName: prescriptionReferralData?.consultation?.pharmacyName,
        pharmacyAddress: prescriptionReferralData?.consultation?.pharmacyAddress,
        prescriptionDate: new Date().toISOString().split('T')[0], // Current date in YYYY-MM-DD format
        ...(selectedDeliveryOption === "delivery" && deliveryAddress && {
          userLocation: {
            address: deliveryAddress.street,
            phoneNumber: prescriptionReferralData?.patient?.phoneNumber || "",
            state: deliveryAddress.state,
            lga: deliveryAddress.lga,
            city: deliveryAddress.city,
            landmark: deliveryAddress.landmark,
            lat: deliveryAddress.latitude,
            lng: deliveryAddress.longitude,
          }
        })
      };

      const drugOrderResult = await createDrugOrder({
        // @ts-expect-error string or undefined fields
        variables: { data: drugOrderData }
      });

      if (!drugOrderResult.data?.enrolleeDrugOrder?.drugOrder?._id) {
        throw new Error("Failed to create drug order");
      }

      const drugOrderId = drugOrderResult.data.enrolleeDrugOrder.drugOrder._id;
      console.log("Drug order created successfully:", drugOrderId);

      // Step 2: Initialize payment
      console.log("Initializing payment...");

      const paymentInitData = {
        user: prescriptionReferralData?.patient?._id,
        reason: "drug_order",
        amount: totalAmount,
        email: prescriptionReferralData?.patient?.email,
        itemId: drugOrderId,
        callback_url: `${window.location.origin}/drug-payment-success/${drugOrderId}`,
        saveCard: true
      };

      const paymentResult = await initializePayment({
        // @ts-expect-error string or undefined fields
        variables: { data: paymentInitData }
      });

      if (paymentResult.data?.initPayment?.errors && paymentResult.data.initPayment.errors.length > 0) {
        const errorMessages = paymentResult.data.initPayment.errors.map(err => err.message).join(", ");
        throw new Error(`Payment initialization faileds: ${errorMessages}`);
      }

      if (!paymentResult.data?.initPayment?.paymentInitResponse?.authorization_url) {
        throw new Error("No authorization URL received from payment service");
      }

      console.log("Payment initialized successfully");

      // Redirect to payment gateway
      window.location.href = paymentResult.data.initPayment.paymentInitResponse.authorization_url;

    } catch (error) {
      console.error("Error processing order:", error);

      toast({
        title: "Order Processing Failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessingOrder(false);
    }
  };

  const handleClose = () => {
    // Navigate back or to a specific page
    navigate({ to: "/" });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <HealaLoader />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen p-4">
        <div className="text-red-500 text-xl mb-4">Error: {error}</div>
        <Button onClick={() => window.location.reload()}>Try Again</Button>
      </div>
    );
  }

  if (!paymentData) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen p-4">
        <div className="text-red-500 text-xl mb-4">Error: No payment data found</div>
        <Button onClick={() => window.location.reload()}>Try Again</Button>
      </div>
    );
  }

  const selectedOption = paymentData.deliveryOptions.find(
    option => option.id === selectedDeliveryOption
  );

  const deliveryPrice = selectedOption?.price || 0;
  const subtotal = paymentData.drugs.reduce((sum, drug) => sum + ((drug.markedUpDrugPrice || 0) * drug.quantity), 0);
  const total = subtotal + deliveryPrice;

  return (
    <div className="flex flex-col justify-center items-center min-h-screen bg-gray-50 p-4">
      {/* Heala Logo */}
      <div className="mb-10 mt-4 lg:mt-6">
        <HealaLogoWithText />
      </div>

      <div className="bg-white rounded-lg shadow-md w-full max-w-2xl relative">
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
          aria-label="Close"
        >
          <X size={20} />
        </button>

        <div className="p-6">
          <h1 className="text-2xl font-bold mb-6">Checkout</h1>

          <div className="border-t border-gray-200 my-4"></div>

          <DrugList drugs={paymentData.drugs} />

          <div className="border-t border-gray-200 my-4"></div>

          <DeliveryOptions
            options={paymentData.deliveryOptions}
            selectedOptionId={selectedDeliveryOption || ''}
          />

          <div className="border-t border-gray-200 my-4"></div>

          <PaymentSummary
            subtotal={subtotal}
            deliveryPrice={deliveryPrice}
            total={total}
          />

          <Button
            className="w-full mt-6 py-6"
            onClick={handlePlaceOrder}
            disabled={isProcessingOrder}
          >
            {isProcessingOrder ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing Order...
              </>
            ) : (
              "Place Order"
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}

