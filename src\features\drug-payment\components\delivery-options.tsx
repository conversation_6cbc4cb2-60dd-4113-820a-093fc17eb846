import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { DeliveryOption } from "../types";
import { useDrugPaymentStore } from "../hooks/use-drug-payment-store";
import { Label } from "@/components/ui/label";
import { DeliveryAddressSection } from "./delivery-address-section";

interface DeliveryOptionsProps {
  options: DeliveryOption[];
  selectedOptionId: string;
}

export function DeliveryOptions({ options, selectedOptionId }: DeliveryOptionsProps) {
  const { setSelectedDeliveryOption } = useDrugPaymentStore();

  return (
    <div>
      <RadioGroup
        value={selectedOptionId}
        onValueChange={setSelectedDeliveryOption}
        className="space-y-3"
      >
        {options.map((option) => (
          <div
            key={option.id}
            className={`p-4 rounded-lg cursor-pointer ${selectedOptionId === option.id
                ? "bg-blue-50 border border-blue-200"
                : "bg-gray-50"
              }`}
            onClick={() => setSelectedDeliveryOption(option.id)}
          >
            {/* Desktop layout: horizontal */}
            <div className="hidden sm:flex items-start">
              <RadioGroupItem
                value={option.id}
                id={option.id}
                className="mt-1"
              />
              <div className="ml-3 flex-grow">
                <Label
                  htmlFor={option.id}
                  className="font-medium text-gray-900 cursor-pointer"
                >
                  {option.name}
                </Label>
                <p className="text-sm text-gray-500 mt-1">{option.description}</p>
              </div>
              <div className="ml-2 text-right">
                {option.price === 0 ? (
                  <span className="text-emerald-600 font-medium">Free</span>
                ) : (
                  <span className="text-gray-900 font-medium">
                    ₦{option.price.toLocaleString('en-NG', { minimumFractionDigits: 2 })}
                  </span>
                )}
              </div>
            </div>

            {/* Mobile layout: stacked vertically */}
            <div className="sm:hidden">
              <div className="flex items-start">
                <RadioGroupItem
                  value={option.id}
                  id={option.id}
                  className="mt-1"
                />
                <div className="ml-3 flex-grow">
                  <Label
                    htmlFor={option.id}
                    className="font-medium text-gray-900 cursor-pointer"
                  >
                    {option.name}
                  </Label>
                  <p className="text-sm text-gray-500 mt-1">{option.description}</p>
                  <div className="mt-2">
                    {option.price === 0 ? (
                      <span className="text-emerald-600 font-medium">Free</span>
                    ) : (
                      <span className="text-gray-900 font-medium">
                        ₦{option.price.toLocaleString('en-NG', { minimumFractionDigits: 2 })}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </RadioGroup>

      {/* Show delivery address section when delivery option is selected */}
      <DeliveryAddressSection show={selectedOptionId === "delivery"} />
    </div>
  );
}

